/**********************************************************************
* NXEcProAsdu101_GWS.cpp         author:mjr      date:06/09/2021        
*---------------------------------------------------------------------
*  note: 国网ASDU103报文转换处理-召唤通用文件列表                                                            
*  
**********************************************************************/

#include "NXEcProAsdu101_GWS.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无

TNXEcProAsdu103GWS::~TNXEcProAsdu103GWS()
{

}
*/
/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu101GWS::TNXEcProAsdu101GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu101(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu101GWS");
	bRunCheckPoint101 = false;
	m_using_area_101 = 0;
}

/**
* @brief		从召唤文件命令报文中,解析出文件名称(不包含路径)	
* @param[in]    PRO_FRAME_BODY * pBody:命令报文指针
* @param[out]   char * cFindFileName:文件名称  
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu101GWS::__GetFileNameFormProFrameBody(IN PRO_FRAME_BODY * pBody,OUT char * cFindFileName)
{
	char cError[255] = "";
	char cFileName[100]="";
	if ( 1 == m_using_area_101)		//新疆地区读取主站下发的文件名
	{
		const char *ctemp = "CIME/DEVINFO" ;	//加入"CIME/DEVINFO"字符串供比较，下的命令是否为全站装置配置信息的文件列表召唤
		vector<string> vStr;
		memcpy(cFileName,&(pBody->vVarData[15]),100);	
		sprintf(cError,"从主站下发的文件名称字符串为：%s .",cFileName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

		__sm_split(cFileName,FILE_PATH_OPT_CHAR,vStr);

		if ( 0 == strcmp(cFileName,ctemp) ) //下发的是特殊字符，主站要去召唤配置信息文件
		{
			sprintf(cError,"与基准字符串比较 %s ，一致则表示全站装置配置信息的文件列表召唤.",ctemp);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");

			memcpy(cFindFileName,"*_DEVINFO.CIME",14);	//把"*_DEVINFO.CIME"写入本函数的返回内容cFindFileName中
			sprintf(cError,"通用文件召唤命令中,指定的文件名为:%s.",cFindFileName);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
			vStr.clear();
			return 0;
		}
		else //主站下发的不是特殊字符，此时按照一般通用文件列表上送
		{
			vector<string> vStr;
			memcpy(cFileName,&(pBody->vVarData[15]),100);	
			__sm_split(cFileName,FILE_PATH_OPT_CHAR,vStr);
			if (vStr.size() == 0)
			{
				sprintf(cError,"从主站下发的文件名称字符串%s中，提取文件名（不含路径）失败，将上送所有的通用文件.",cFileName);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu101");
				memcpy(cFindFileName,"*",1);
			}
			else
			{
				//下发文件名为空，崩溃问题
				memcpy(cFindFileName,vStr[vStr.size()-1].c_str(),vStr[vStr.size()-1].length());
			}		
			sprintf(cError,"通用文件召唤命令中,指定的文件名为:%s.",cFindFileName);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101");
			vStr.clear();
			return 0;
		}
	}
	else	//一般地区的读取主站下发的文件名
	{
		vector<string> vStr;
		memcpy(cFileName,&(pBody->vVarData[15]),100);	
		__sm_split(cFileName,FILE_PATH_OPT_CHAR,vStr);
		if (vStr.size() == 0)
		{
			sprintf(cError,"从主站下发的文件名称字符串%s中，提取文件名（不含路径）失败，将上送所有的通用文件.",cFileName);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu101");
			memcpy(cFindFileName,"*",1);
		}
		else
		{
			//下发文件名为空，崩溃问题
			memcpy(cFindFileName,vStr[vStr.size()-1].c_str(),vStr[vStr.size()-1].length());
		}
		sprintf(cError,"通用文件召唤命令中,指定的文件名为:%s.",cFindFileName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101");
		vStr.clear();
		return 0;
	}
}


/**
* @brief         直接从本地生成结果回应，如初始化配置;---召唤通用文件列表
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu101GWS::DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255]="";
	sprintf(cError,"通用文件列表召唤操作");
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

	if (!bRunCheckPoint101)
	{
		//如果首次启动，需要初始化
		__ReadGw103Ini_101();
	}

	// 首先检查是否包含SettingUp关键字（所有地区都支持）
	if (pBody->vVarData.size() >= 115)
	{
		char cFileName[100]="";
		memcpy(cFileName,&(pBody->vVarData[15]),100);
		string strFullFname = cFileName;
		char *psettingfile = _strstr_nocase((char*)strFullFname.c_str(), "SettingUp");

		if (NULL != psettingfile)
		{
			RcdErrLogWithParentClass("DirectResFromLocal:命令中的文件名含SettingUp","TNXEcProAsdu101GWS");

			//获取返回信息标示符:
			m_nRii=(u_int8)pBody->nRii;

			//获取命令中的时间范围
			ASDU_TIME AsduTime;
			__GetTimeFormProFrameBody(pBody,AsduTime);

			//把SettingUp作为分隔符，将SettingUp前面的替换成定值根路径，将后面的拼上去，作为定值路径。
			//从前往后找SettingUp
			int npos = strFullFname.find("SettingUp");
			string strName = "";
			if (npos != string::npos)
			{
				strName = strFullFname.substr(npos+9);//得到SettingUp后面的的路径包括/
			}

			//到通用文件夹下查找相关文件并返回结果
			FILE_PROPERTY_INF_LIST GeneralFileList;
			if (__QueryGeneralFilesList_SettingUp(AsduTime,(char*)strName.c_str(),pBody,GeneralFileList) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}

			//根据结果做成返回规约的结果报文体
			if (FormatGeneralFilesListBodyofAsdu102(AsduTime,pBody,GeneralFileList,lResult) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}
			GeneralFileList.clear();
			return EC_PRO_CVT_SUCCESS;
		}
	}

	// 如果不是SettingUp，则按原来的地区逻辑处理
	if ( 1 == m_using_area_101)
	{
		sprintf(cError,"此时m_using_area=%d 为新疆地区的通用文件列表召唤操作",m_using_area_101);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

		if (pBody->vVarData.size() < 115)
		{
			RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件列表命令报文长度不足,不进行处理.","TNXEcProAsdu101GWS");
			return EC_PRO_CVT_FAIL;
		}
		//获取返回信息标示符:
		m_nRii=(u_int8)pBody->nRii;

		//获取命令中的时间范围
		ASDU_TIME AsduTime;
		__GetTimeFormProFrameBody(pBody,AsduTime);

		//获取命令中的查询文件名
		char cFindFileName[255]="";
		if (__GetFileNameFormProFrameBody(pBody,cFindFileName) < 0 )
		{
			RcdErrLogWithParentClass("DirectResFromLocal:解析命令中的文件名失败，请检查命令中的文件名是否为空。","TNXEcProAsdu101GWS");
			return EC_PRO_CVT_FAIL;
		}
		//此时获取命令完成，拿着转化完成后的路径去找本地文件
		const char *ctemp = "*_DEVINFO.CIME";	//判断是否为召唤的是不是特别的
		if ( 0 == strcmp(cFindFileName,ctemp) )
		{
			AsduTime.nInfoHappenUtc = 0;
			AsduTime.nInfoHappenMs  = 0;
			AsduTime.nInfoRcvUtc = 0;
			AsduTime.nInfoRcvMs  = 0;
			//把起止时间置零表示时间无效
			sprintf(cError,"置零后的时间为：%d .",AsduTime.nInfoHappenUtc);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

			sprintf(cError,"待传入的查找文件名为：%s .",cFindFileName);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
			//到通用文件夹下查找相关文件并返回结果
			FILE_PROPERTY_INF_LIST GeneralFileList;
			if (__QueryGeneralFilesList(AsduTime,cFindFileName,GeneralFileList) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}

			//根据结果做成返回规约的结果报文体
			if (FormatGeneralFilesListBodyofAsdu102(AsduTime,pBody,GeneralFileList,lResult) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}
			GeneralFileList.clear();
			return EC_PRO_CVT_SUCCESS;

		}
		else //没有下发特定字符，检查是否为一般通用文件
		{
			sprintf(cError,"待传入的查找文件名为：%s .",cFindFileName);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

			//到通用文件夹下查找相关文件并返回结果
			FILE_PROPERTY_INF_LIST GeneralFileList;
			//一般通用文件处理
			if (__QueryGeneralFilesList(AsduTime,cFindFileName,GeneralFileList) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}

			//根据结果做成返回规约的结果报文体
			if (FormatGeneralFilesListBodyofAsdu102(AsduTime,pBody,GeneralFileList,lResult) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}
			GeneralFileList.clear();
			return EC_PRO_CVT_SUCCESS;

		}
	}
	else	//是一般地区，不需要新疆地区扩展功能
	{
		char cError[255]="";
		RcdTrcLogWithParentClass("开始响应召唤通用文件列表命令.","TNXEcProAsdu101");
		if (pBody->vVarData.size() < 115)
		{
			RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件列表命令报文长度不足,不进行处理.","TNXEcProAsdu101");
			return EC_PRO_CVT_FAIL;
		}
		//获取返回信息标示符:
		m_nRii=(u_int8)pBody->nRii;

		//获取命令中的时间范围
		ASDU_TIME AsduTime;
		__GetTimeFormProFrameBody(pBody,AsduTime);

		//20240102
		char *plogfile = NULL;
		char cFileName[100]="";
		memcpy(cFileName,&(pBody->vVarData[15]),100);
		string strFullFname = cFileName;
		//log文件目录
		plogfile = _strstr_nocase((char*)strFullFname.c_str(), "nxlog");
		//到通用文件夹下查找相关文件并返回结果
		FILE_PROPERTY_INF_LIST GeneralFileList;
		if (NULL != plogfile)
		{
			RcdErrLogWithParentClass("DirectResFromLocal:命令中的文件名含nxlog","TNXEcProAsdu101GWS");
			//把nxlog作为分隔符，将nxlog前面的替换成日志根路径，将后面的拼上去，作为日志路径。
			//从前往后找nxlog
			int npos = strFullFname.find("nxlog");
			string strName = "";
			if (npos != string::npos)
			{
				strName = strFullFname.substr(npos+5);//得到nxlog后面的的路径包括/
			}

			if (__QueryGeneralFilesList_nxlog(AsduTime,(char*)strName.c_str(),GeneralFileList) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}
		}
		else
		{
			//获取命令中的查询文件名
			char cFindFileName[255]="";
			if (__GetFileNameFormProFrameBody(pBody,cFindFileName) < 0 )
			{
				RcdErrLogWithParentClass("DirectResFromLocal:解析命令中的文件名失败，请检查命令中的文件名是否为空。","TNXEcProAsdu101");
				return EC_PRO_CVT_FAIL;
			}

			if (__QueryGeneralFilesList(AsduTime,cFindFileName,GeneralFileList) < 0)
			{
				return EC_PRO_CVT_FAIL;
			}
		}


		//根据结果做成返回规约的结果报文体
		if (FormatGeneralFilesListBodyofAsdu102(AsduTime,pBody,GeneralFileList,lResult) < 0)
		{
			return EC_PRO_CVT_FAIL;
		}
		GeneralFileList.clear();
		return EC_PRO_CVT_SUCCESS;
	}
}

/**
* @brief		根据指定的文件名,获取通用文件夹下的对应文件,组成列表返回			
* @param[in]    ASDU_TIME AsduTime:时间范围
* @param[in]    const char * cFindFileName:文件名
* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu101GWS::__QueryGeneralFilesList(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList)
{
	char cError[255] = "";

	//获取路径信息
	char cGeneralFilePath[500]="";
	char cTemp[500]="";
	char cFileName[255] = "";
	char cFindName[255] = "";
	memcpy(cFindName,cFindFileName,strlen(cFindFileName));

	vector<string> vStr;

	BASIC_CFG_TB CfgTb;
	if (!m_pModelSeek->GetBasicCfg(CfgTb)) 
	{
		RcdErrLogWithParentClass("__QueryGeneralFilesList:读取系统基本配置表信息失败。","TNXEcProAsdu101GWS");
		return -1;
	}
	if (sy_format_file_path(CfgTb.str_file_path.c_str(),cTemp) < 0) return EC_PRO_CVT_FAIL;
	sprintf(cGeneralFilePath,"%s%s",cTemp,"通用文件/");		//win下需在“通用文件”后加“\”不然系统识别为文件而文件夹
															//Linux下不能加这个\

	//获取路径下的所有文件信息
	FILE_PROPERTY_INF_LIST FileInfoList;
	if (0 > sy_get_files_property_indir(cGeneralFilePath,&FileInfoList) )				//获取通用文件夹下文件信息列表。
	{
		sprintf(cError,"获取路径:%s下的全部文件时失败，可能的原因为路径不存在，或者路径格式不正确。",cGeneralFilePath);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");;
	}
	sprintf(cError,"匹配目录为:%s全部文件数（%d）.",cGeneralFilePath,FileInfoList.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

	FILE_PROPERTY_INF_LIST::iterator ite=FileInfoList.begin();
	while(ite != FileInfoList.end())
	{
		if ( (0 != AsduTime.nInfoRcvUtc)  &&  (0 != AsduTime.nInfoHappenUtc) )			//如果时间范围都为0,则表明不按时间范围去取文件.
		{
			if ((ite->nLastTime > AsduTime.nInfoRcvUtc) || (ite->nLastTime < AsduTime.nInfoHappenUtc)) //文件不在时间范围
			{
				ite++;
				continue;
			}
		}		
		_ZERO_MEM(cFileName,255);
		__sm_split(ite->chName,FILE_PATH_OPT_CHAR,vStr);
		memcpy(cFileName,vStr[vStr.size()-1].c_str(),vStr[vStr.size()-1].length());

		sprintf(cError,"%s <------------>%s .",cFindName,cFileName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		if (__wildcard_matches(cFindName,cFileName))  //文件名匹配.
		{
			_ZERO_MEM(ite->chName,strlen(ite->chName));
			memcpy(ite->chName,cFileName,strlen(cFileName));
			GeneralFileList.push_back(*ite);
			sprintf(cError,"匹配到文件:%s.",cFileName);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		}

		_ZERO_MEM(cFileName,255);
		vStr.clear();
		ite++;
	}
	FileInfoList.clear();
	return 0;
}

void TNXEcProAsdu101GWS::__ReadGw103Ini_101()
{
	int nTemp;
	char cError[255] = "";

	CIniOperate clMyIni;
	if (!clMyIni.SetIniFileName("Gw103.ini")) 
	{
		printf("[CYKStrapRead:__YKReadIni] 没有找到配置文件'Gw103.ini'!.\n");
	}
	else	
	{
		clMyIni.ParseIni();
		clMyIni.GetPrivateProfileInt("SYS","using_area",0,nTemp);//0-默认为一般地区（默认扩展功能关闭） 1-特指新疆地区（功能开启）
		m_using_area_101 = nTemp;
		sprintf(cError,"读取到Gwini文件中的m_using_area_101为 %d ",m_using_area_101);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
	}

	bRunCheckPoint101 = true;			//初始化后，就无需进入次函数再次初始化了

}

/**
* @brief         字符串不区分大小写匹配
* @param[in]     const string & s : 匹配字符串
* @param[in]     const string & s2 :匹配字符串
* @return        
*/
char* TNXEcProAsdu101GWS::_strstr_nocase(const char * str1, const char *  str2)
{
	char *cp = (char*)str1;
	char *s1,*s2;
	//如果传进str2是空,返回str1
	if (!*str2)
	{
		return ((char*)str1);
	}

	while(*cp)
	{
		s1 = cp;
		s2 = (char *)str2;

		while( *s1 && *s2 && toupper(*s1)==toupper(*s2))
		{
			s1++;
			s2++;
		}

		if (!*s2)
		{
			return cp;
		}

		cp++;
	}
	return NULL;
}

/**
* @brief		根据指定的文件名,获取nxlog文件夹下的对应文件,组成列表返回			
* @param[in]    ASDU_TIME AsduTime:时间范围
* @param[in]    const char * cFindFileName:文件名
* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu101GWS::__QueryGeneralFilesList_nxlog(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList)
{
	char cError[255] = "";

	//获取路径
	char cGeneralFilePath[500] = "";
	char cTemp[500] = "";
	char cFileName[255]="";
	char cFindName[255]="";
	memcpy(cFindName,cFindFileName,strlen(cFindFileName));
	
	//去数据库查日志配置路径
	BASIC_CFG_TB CfgTb;
	if (!m_pModelSeek->GetBasicCfg(CfgTb)) 
	{
		RcdErrLogWithParentClass("__QueryGeneralFilesList:读取系统基本配置表信息失败。","TNXEcProAsdu101GWS");
		return -1;
	}
	if (sy_format_file_path(CfgTb.str_logroot_path.c_str(), cTemp) < 0) return EC_PRO_CVT_FAIL;//日志根路径
	char cFile[255]="";
	sprintf(cFile,"%s%s",cTemp,cFindName);
	if (sy_format_file_path(cFile, cGeneralFilePath) < 0) return EC_PRO_CVT_FAIL;

	char cFilePath[500]="";
	memset(cTemp,0,500);
	sprintf(cTemp,"%s%s","/nxlog",cFindName);
	sy_format_file_path(cTemp, cFilePath);
	int nFilePathLen = strlen(cFilePath);//用来获取返回的相对路径
		
	//获取路径下的所有文件信息
	FILE_PROPERTY_INF_LIST FileInfoList;
	if (0 > sy_get_files_property_indir(cGeneralFilePath,&FileInfoList) )				//获取通用文件夹下文件信息列表。
	{
		sprintf(cError,"获取路径:%s下的全部文件时失败，可能的原因为路径不存在，或者路径格式不正确。",cGeneralFilePath);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");
	}
	sprintf(cError,"匹配目录为:%s全部文件数（%d）.",cGeneralFilePath,FileInfoList.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

	FILE_PROPERTY_INF_LIST::iterator ite=FileInfoList.begin();
	while(ite != FileInfoList.end())
	{
		//不考虑时间范围
// 		if ( (0 != AsduTime.nInfoRcvUtc)  &&  (0 != AsduTime.nInfoHappenUtc) )			//如果时间范围都为0,则表明不按时间范围去取文件.
// 		{
// 			if ((ite->nLastTime > AsduTime.nInfoRcvUtc) || (ite->nLastTime < AsduTime.nInfoHappenUtc)) //文件不在时间范围
// 			{
// 				ite++;
// 				continue;
// 			}
// 		}		
		sprintf(cError,"%s <------------>%s .",cFindName,ite->chName);//返回的文件名带了根路径
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

		vector<string> vStr;
		_ZERO_MEM(cFileName,255);
		__sm_split(ite->chName,FILE_PATH_OPT_CHAR,vStr);
		memcpy(cFileName,vStr[vStr.size()-1].c_str(),vStr[vStr.size()-1].length());
		
		_ZERO_MEM(ite->chName,strlen(ite->chName));
		memcpy(ite->chName,cFileName,strlen(cFileName));//获取文件名（不带路径）

		//返回相对路径
		_ZERO_MEM(ite->cReserve1,strlen(ite->cReserve1));
		memcpy(ite->cReserve1,&cFilePath,nFilePathLen);

		//将相对路径和文件名合在一起,作为文件名,这样,原来60870公用中的101报文处理文件就不用修改了.
		_ZERO_MEM(cTemp,500);
		sprintf(cTemp,"%s%s",ite->cReserve1,ite->chName);
		_ZERO_MEM(ite->chName,MAX_FILE_NAME_LEN);
		memcpy(ite->chName,cTemp,strlen(cTemp));

		GeneralFileList.push_back(*ite);
		sprintf(cError,"匹配到路径:%s  文件:%s",ite->cReserve1,ite->chName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		ite++;
	}
	FileInfoList.clear();
	return 0;

}

/**
* @brief		根据指定的文件名,获取Setting文件夹下的对应文件,组成列表返回
* @param[in]    ASDU_TIME AsduTime:时间范围
* @param[in]    const char * cFindFileName:文件名
* @param[in]    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu101GWS::__QueryGeneralFilesList_SettingUp(IN ASDU_TIME AsduTime,IN const char * cFindFileName,IN PRO_FRAME_BODY * pBody,OUT FILE_PROPERTY_INF_LIST & GeneralFileList)
{
	char cError[255] = "";

	//获取路径
	char cGeneralFilePath[500] = "";
	char cTemp[500] = "";
	char cFileName[255]="";
	char cFindName[255]="";
	memcpy(cFindName,cFindFileName,strlen(cFindFileName));

	//去数据库查配置根路径
	BASIC_CFG_TB CfgTb;
	if (!m_pModelSeek->GetBasicCfg(CfgTb))
	{
		RcdErrLogWithParentClass("__QueryGeneralFilesList_SettingUp:读取系统基本配置表信息失败。","TNXEcProAsdu101GWS");
		return -1;
	}
	if (sy_format_file_path(CfgTb.str_file_path.c_str(), cTemp) < 0) return EC_PRO_CVT_FAIL;//文件根路径

	// 从 cFindName 中解析地址（格式：/addr/文件名）
	int nAddr = 0;
	string strFileName = cFindName;
	if (strlen(cFindName) > 1 && cFindName[0] == '/')
	{
		// 查找第二个 '/' 的位置
		char* pSecondSlash = strchr(cFindName + 1, '/');
		if (pSecondSlash != NULL)
		{
			// 提取两个 '/' 之间的地址
			string strAddr(cFindName + 1, pSecondSlash - cFindName - 1);
			nAddr = atoi(strAddr.c_str());
			// 获取文件名部分（第二个 '/' 之后的内容）
			strFileName = string(pSecondSlash);

			sprintf(cError,"__QueryGeneralFilesList_SettingUp:从路径[%s]中解析出地址[%d],文件名[%s]", cFindName, nAddr, strFileName.c_str());
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		}
		else
		{
			sprintf(cError,"__QueryGeneralFilesList_SettingUp:路径格式错误[%s],无法解析地址", cFindName);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");
			return -1;
		}
	}
	else
	{
		sprintf(cError,"__QueryGeneralFilesList_SettingUp:路径格式错误[%s],应以/开头", cFindName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		return -1;
	}

	// 根据解析出的地址获取对应的IED ID
	const IED_TB* pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nAddr);
	if (pIed == NULL)
	{
		sprintf(cError,"__QueryGeneralFilesList_SettingUp:根据地址[%d]获取IED配置失败", nAddr);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		return -1;
	}

	char cFile[255]="";
	// 构建路径：基础路径 + "/SettingUp/" + IED ID + 文件名
	sprintf(cFile,"%s/SettingUp/%d%s",cTemp, pIed->n_obj_id, strFileName.c_str());
	if (sy_format_file_path(cFile, cGeneralFilePath) < 0) return EC_PRO_CVT_FAIL;

	sprintf(cError,"__QueryGeneralFilesList_SettingUp:构建路径[%s],解析地址[%d],IED ID[%d]", cGeneralFilePath, nAddr, pIed->n_obj_id);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

	char cFilePath[500]="";
	memset(cTemp,0,500);
	sprintf(cTemp,"%s%s","/SettingUp",cFindName);
	sy_format_file_path(cTemp, cFilePath);
	int nFilePathLen = strlen(cFilePath);//用来获取返回的相对路径

	//获取路径下的所有文件信息
	FILE_PROPERTY_INF_LIST FileInfoList;
	if (0 > sy_get_files_property_indir(cGeneralFilePath,&FileInfoList) )				//获取Setting文件夹下文件信息列表。
	{
		sprintf(cError,"获取路径:%s下的全部文件时失败，可能的原因为路径不存在，或者路径格式不正确。",cGeneralFilePath);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu101GWS");
	}
	sprintf(cError,"匹配目录为:%s全部文件数（%d）.",cGeneralFilePath,FileInfoList.size());
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

	FILE_PROPERTY_INF_LIST::iterator ite=FileInfoList.begin();
	while(ite != FileInfoList.end())
	{
		//根据时间范围过滤文件
		if ( (0 != AsduTime.nInfoRcvUtc)  &&  (0 != AsduTime.nInfoHappenUtc) )			//如果时间范围都为0,则表明不按时间范围去取文件.
		{
			if ((ite->nLastTime > AsduTime.nInfoRcvUtc) || (ite->nLastTime < AsduTime.nInfoHappenUtc)) //文件不在时间范围
			{
				ite++;
				continue;
			}
		}
		sprintf(cError,"%s <------------>%s .",cFindName,ite->chName);//返回的文件名带了根路径
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");

		vector<string> vStr;
		_ZERO_MEM(cFileName,255);
		__sm_split(ite->chName,FILE_PATH_OPT_CHAR,vStr);
		memcpy(cFileName,vStr[vStr.size()-1].c_str(),vStr[vStr.size()-1].length());

		_ZERO_MEM(ite->chName,strlen(ite->chName));
		memcpy(ite->chName,cFileName,strlen(cFileName));//获取文件名（不带路径）

		//返回相对路径
		_ZERO_MEM(ite->cReserve1,strlen(ite->cReserve1));
		memcpy(ite->cReserve1,&cFilePath,nFilePathLen);

		//将相对路径和文件名合在一起,作为文件名,这样,原来60870公用中的101报文处理文件就不用修改了.
		_ZERO_MEM(cTemp,500);
		sprintf(cTemp,"%s%s",ite->cReserve1,ite->chName);
		_ZERO_MEM(ite->chName,MAX_FILE_NAME_LEN);
		memcpy(ite->chName,cTemp,strlen(cTemp));

		GeneralFileList.push_back(*ite);
		sprintf(cError,"匹配到路径:%s  文件:%s",ite->cReserve1,ite->chName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu101GWS");
		ite++;
	}
	FileInfoList.clear();
	return 0;
}
