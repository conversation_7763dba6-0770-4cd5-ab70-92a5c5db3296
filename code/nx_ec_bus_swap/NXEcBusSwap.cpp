/**********************************************************************
* NXEcBusSwap.cpp         author:jjl      date:13/09/2013            
*---------------------------------------------------------------------
*  note: 总线交互数据类实现文件                                                                
*  
**********************************************************************/

#include "NXEcBusSwap.h"

/**
* @brief         构造函数
* @param[in]     无
* @param[out]    
* @return        无
*/
CNXEcBusSwap::CNXEcBusSwap():CNXECObject(NULL,"CNXEcBusSwap")
{
	m_pEcModelLib   = NULL;
	m_pSubject      = NULL;
	m_pObserver     = NULL;
	m_pModelSeekIns = NULL;
	m_bEnd          = false;
	m_bStart        = false;
	m_nRegisterType = -1; // 表示注册所有IED和主题
	m_EventDeque.clear();

	// 设置日志模块名
	_SetLogModuleName("bus_swap");

	// 设置运行时对象
	SetRunTimeObjName(SHARE_LIB_BUS_SWAP);
}

/**
* @brief         析构函数
* @param[in]     无
* @param[out]    
* @return        无
*/
CNXEcBusSwap::~CNXEcBusSwap()
{
	StopBusService();
}

/**
* @brief         获取总线交换数据类单实例指针
* @param[in]     无
* @param[out]    无
* @return        CNXMainController * :对象指针
*/
CNXEcBusSwap * CNXEcBusSwap::GetNXEcBusSwapIns()
{
	if ( NULL == CNXEcBusSwap::sm_pBusSwapIns )
	{
		CNXEcBusSwap::sm_pBusSwapIns = new CNXEcBusSwap();
	}

	return CNXEcBusSwap::sm_pBusSwapIns;
}

/**
* @brief         启动总线交换业务
* @param[in]     bool bPrintLogToScreen:是否打印日志到屏幕
* @param[in]     int  nRegisterType :   向总线注册类型,默认-1表示全部ied和主题,0表示仅注册当前站点的IED
* @param[in]     int  nReserver :       预留
* @param[out]    
* @return        bool :true-成功 false-失败
*/
bool CNXEcBusSwap::StartBusService(bool bPrintLogToScreen,IN int nRegisterType,IN int nReserver)
{
	if( m_bStart )
	{
		RecordErrorLog("StartBusService()已经启动,请先停止后再启动");
		return true;
	}

	// 设置结束标识
	m_bEnd = false;

	// 设置日志是否打印到屏幕
	_SetLogPrint2Screen(bPrintLogToScreen);

	// 设置总线注册类型
	m_nRegisterType = nRegisterType;
	
	// 打开日志
	_OpenLogRecord();

	// 初始化
	if( !_Init() )
	{
		_FreeResource();
		return false;
	}

	// 启动
	m_bStart = _Start();

	return m_bStart;
}

/**
* @brief         停止总线交换业务
* @param[in]     无
* @param[out]    
* @return        bool :true-成功 false-失败
*/
bool CNXEcBusSwap::StopBusService()
{
	if( m_bEnd )
	{
		RecordErrorLog("StopBusService()已经停止,请先启动后再停止");
		return true;
	}

	// 置结束标识
	m_bEnd  = true;

	// 停止线程
	StopAllThread();

	// 释放资源
	_FreeResource();

	// 关闭日志
	_CloseLogRecord();

	m_bStart = false;

	RecordTraceLog("StopBusService():停止总线服务完毕");
	return true;
}

/**
* @brief         完成各对象资源分配及业务初始化
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool   CNXEcBusSwap::_Init()
{
	// 加载对外通信内部相关共享库
	if( __InitEcLib() != 0 )
	{
		return false;
	}

	// 初始化目标者对象
	if( __InitSubject() != 0 )
	{
		return false;
	}

	// 加载mb client 共享库
	if( __InitMbAppClient() != 0 )
	{
		return false;
	}

	// 初始化线程信息
	if( __InitThreadInfo() != 0 )
	{
		return false;
	}

	// 初始化观察者
	if( __InitObserver() != 0 )
	{
		return false;
	}

	RecordTraceLog("_Init()成功");
	return true;
}

/**
* @brief         释放对象资源及共享库
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcBusSwap::_FreeResource()
{
	try
	{
		// 停止总线服务
		if( m_MbClientLib.StopNxmb() != 0 )
		{
			RecordErrorLog("_FreeResource()停止消息客户端库失败");
		}

		// 释放mb client库
		__FreeMbAppClient();
	}
	catch (...)
	{
		RecordErrorLog("_FreeResource()停止总线服务异常");
	}
	
	// 释放目标者
	__FreeSubject();

	// 释放观察者
	__FreeObserver();

	// 释放对外通信相关共享库
	__FreeEcLib();

	// 释放线程资源
	StopAllThread();

	// 清空事件队列
	NX_EVENT_MESSAGE EventMsg;
	while(!m_EventDeque.empty())
	{
		EventMsg = m_EventDeque.front();
		EventMsg.list_subfields.clear();
		m_EventDeque.pop_front();
	}

	// 请空命令队列
	NX_COMMON_MESSAGE CmdMsg;
	while( !m_CommonMsgDeque.empty() )
	{
		CmdMsg = m_CommonMsgDeque.front();
		CmdMsg.list_subfields.clear();
		m_CommonMsgDeque.pop_front();
	}

	return true;
}

/**
* @brief         启动各项业务及分发处理线程等
* @param[in]     无
* @param[out]    无
* @return        bool :true-成功 false-失败
*/
bool CNXEcBusSwap::_Start()
{
	try
	{
		// 启动总线收发服务
		if( m_MbClientLib.StartNxmb(m_MbAppNode) != 0 )
		{
			RecordErrorLog("_Start()中启动总线服务失败");
			return false;
		}
	}
	catch(...)
	{
		RecordErrorLog("_Start()中启动总线服务异常");
		return false;
	}
	
	// 启动所有线程
	bool bOk = StartAllThread();

	return bOk;
}

/**
* @brief         目标者对象资源分派，设置注册信息等
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__InitSubject()
{
	if( m_pSubject != NULL )
	{
		RecordErrorLog("__InitSubject()时目标者已经初始化，不再初始化");
		return 0;
	}

	// 分配资源
	m_pSubject = new CNXSubject(m_pLogRecord,BUS_SWAP_SUBJECT_ID);
	if( NULL == m_pSubject )
	{
		RecordErrorLog("__InitSubject()时为目标者分配资源失败");
		return COMMONERR_NEW_MEM_FAILED;
	}

	// 设置目标者功能描述
	m_pSubject->SetRegObjDesc("BUS_SWAP目标者");

	// 设置其所管理的IED设备(所有设备召唤命令都需通过该目标者发送到总线,故为管理所有IED设备)
	EC_DEV_ORDER_LIST DevList;
	DEV_UUID DevID;
	DevID.eDevType = DEV_SECOND;
	DevID.nDevID   = ALL_DEV;
	DevList.push_back(DevID);
	m_pSubject->SetManagerDev(DevList); // 管理所有IED
    DevList.clear();

	// 设置请求方命令处理回调
	m_pSubject->SetCmdCallBack(this,__OnObserverCmdRecv);

	// 注册到服务中介
	if( !m_pSubject->Init() )
	{
		return -1;
	}
	return 0;
}

/**
* @brief         释放目标者信息资源，从服务中介注销
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__FreeSubject()
{
	if( m_pSubject != NULL )
	{
		m_pSubject->Exit();

		delete m_pSubject;

		m_pSubject = NULL;
	}

	return 0;
}

/**
* @brief         初始化总线客户端、向总线注册关注信息类型、注册回调、初始化结点信息等；
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__InitMbAppClient()
{
	try
	{
		// 加载共享库
		if( m_MbClientLib.Initialize() != 0)
		{
			RecordErrorLog("__InitMbAppClient()中加载总线客户端共享库失败");
			return COMMONERR_LOAD_LIB_FAILED;
		}

		// 设置应用程序结点属性和消息主题
		if( __SetAppNodeAndTopic() != 0 )
		{
			RecordErrorLog("__InitMbAppClient()中向总线注册节点属性和主题失败");
			return -1;
		}

		// 注册消息回调函数
		m_MbClientLib.RegcallbackfunCommonMsg((PFUN_NX_MB_COMMON_MESSAGE)__OnBusCommonMsgRecv,this);
		m_MbClientLib.RegcallbackfunEventMsg((PFUN_NX_MB_EVENT_MESSAGE)__OnBusEventMsgRecv,this);
	}
	catch(...)
	{
		RecordErrorLog("__InitMbAppClient()中向总线注册结点属性和主题异常");
		return -1;
	}
	return 0;
}

/**
* @brief         设置应用程序结点属性和消息主题
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__SetAppNodeAndTopic()
{
	NXMB_SUB_OBJ subObj;         // 结点子对象
	// 进程名称
	char cProcessName[96] = "nx_ec_service";   

	// 设置结点类型
	m_MbAppNode.programtype = NX_APP_DEV_ECU;

	if( m_nRegisterType == -1 ) // 注册所有主站客户端对象和主题(对上通信)
	{
		// 设置进程名称
		sprintf(m_MbAppNode.programname,"%s",cProcessName);

		// 设置结点管理的子对象(管理所有的主站客户端)
		subObj.subobjtype = NX_OBJ_TYPE_NX_ZHUZHAN;   // 主站客户端
		sprintf(subObj.subobjname,"%s","all");        // 全部
		
		m_MbAppNode.subobjlist.push_back(subObj);

		// 设置关注消息主体
		m_MbClientLib.RegAllMsgTopics();
	}
	else if( m_nRegisterType == 0 ) // 注册指定的ied和主题(对下通信,作为主站与子站通信）
	{
		// 进程名称

		// 设置管理的子节点

		// 设置关注消息主体(不需要关注事件类主题)
	}
	else
	{
		RecordErrorLog("__SetAppNodeAndTopic()中注册类型=%d,无效,向总线注册失败");
		return -1;
	}
	return 0;
}


/**
* @brief         向总线注销自身、释放资源等
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__FreeMbAppClient()
{
	int nRet = 0;

	// 注销结点
	if( (nRet =m_MbClientLib.LogoutNxmb()) != 0 )
	{
		RecordErrorLog("__FreeMbAppClient()注销结点失败");
	}

	// 卸载动态库
	if( (nRet = m_MbClientLib.UnInitialize() ) != 0 )
	{
		RecordErrorLog("__FreeMbAppClient()卸载总线客户端库失败");
	}

	return nRet;
}

/**
* @brief         请求者命令接收处理(由目标者回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     NX_COMMON_MESSAGE & CmdMsg:收到的命令信息
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__OnObserverCmdRecv(LPVOID pRegObj,NX_COMMON_MESSAGE & CmdMsg)
{
	char cError[255] = "";
	// 调用mb client接口发送命令到总线
	if( pRegObj == NULL )
	{
		printf("__OnObserverCmdRecv()中回调对象为NULL,无法接收请求者命令\n");
		return -1;
	}

	CNXEcBusSwap * pThis = (CNXEcBusSwap *)pRegObj;

	pThis->m_CommonMsgDeque.push_back(CmdMsg);
	
	return 0;
}


/**
* @brief         请求结果或外部应用模块控制命令接收回调（由总线客户端库回调)
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     NX_COMMON_MESSAGE&: 通用消息内容
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
PFUN_NX_MB_COMMON_MESSAGE CNXEcBusSwap::__OnBusCommonMsgRecv(NX_COMMON_MESSAGE& CommonMsg,LPVOID pObj)
{
	int  nRet = 0;
	if( pObj == NULL )
	{
		printf("__OnBusCommonMsgRecv()从总线接收通用信息时，回调对象为NULL,无法接收\n");
		return 0;
	}

	CNXEcBusSwap* pThis = (CNXEcBusSwap*)pObj;

	string strLog = "__OnBusCommonMsgRecv()从总线接收到通用消息,内容如下:\n" + _get_commonmsg_desc(CommonMsg);
	pThis->RecordTraceLog(strLog.c_str());

	// 分类处理
	switch( CommonMsg.n_msg_type )
	{
	case NX_SYS_CALL_ECUSTATUS_ASK:  // 系统命令(召唤与对外通信客户端的通信状态,发送给对外通信管理模块)
		nRet = pThis->m_pObserver->SendCommand(CommonMsg,NODE_MGR_SUBJECT_ID);
		break;
	//case:                          // 通信规约或进程热启动命令发送给主控模块
	//	break;
	default:                         // 召唤结果回送请求者
		nRet = pThis->m_pSubject->SendResult(CommonMsg);
		break;
	}

	CommonMsg.list_subfields.clear();
	return (PFUN_NX_MB_COMMON_MESSAGE)nRet;
}

/**
* @brief         事件信息接收回调（由总线客户端库回调)
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     NX_EVENT_MESSAGE&: 事件消息内容
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
PFUN_NX_MB_EVENT_MESSAGE CNXEcBusSwap::__OnBusEventMsgRecv(NX_EVENT_MESSAGE& EventMsg,LPVOID pObj)
{
	if( pObj == NULL )
	{
		printf("__OnBusEventMsgRecv()从总线接收事件信息时，回调对象为NULL,无法接收\n");
		return (PFUN_NX_MB_EVENT_MESSAGE)-1;
	}

	// 添加到事件队列
	CNXEcBusSwap* pThis = (CNXEcBusSwap*)pObj;

	pThis->m_EventDeque.push_back(EventMsg);

	string strLog = "__OnBusEventMsgRecv()从总线接收到事件,内容如下:\n" + _get_eventmsg_desc(EventMsg);
	
	pThis->RecordTraceLog(strLog.c_str());

	EventMsg.list_subfields.clear();
	return 0;
}

/**
* @brief         将要开辟的各线程信息初始化后加入线程队列
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__InitThreadInfo()
{
	//增加线程参数信息
	EC_THREAD_INFO * pThreadInfo = new EC_THREAD_INFO;
	pThreadInfo->pSelfObj         = this;
	pThreadInfo->strThreadDes     ="事件分发__EventAllotLoop()";
	pThreadInfo->pCallBackFunc    =__OnEventAllotThreadExec;
	AddThreadInfo(pThreadInfo);

	pThreadInfo = NULL;
	pThreadInfo = new EC_THREAD_INFO;
	pThreadInfo->pSelfObj         = this;
	pThreadInfo->strThreadDes     ="命令处理__CmdHandleLoop()";
	pThreadInfo->pCallBackFunc    =__OnCmdHandleThreadExec;
	AddThreadInfo(pThreadInfo);
	return 0;
}

/**
* @brief         事件分发给订阅者处理线程回调执行函数
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     LPVOID pParam: 参数
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcBusSwap::__OnEventAllotThreadExec(LPVOID pObj,LPVOID  pParam /* = NULL*/)
{
	char cError[255]    = "";

	if( NULL == pObj )
		return THREAD_RET_VALUE;

	CNXEcBusSwap * pThis = (CNXEcBusSwap *)pObj;

	try
	{
		pThis->__EventAllotLoop();
	}
	catch(...)
	{
		sprintf(cError,"事件分发线程异常退出,原因:%s:(%d)",strerror(errno),errno);
		pThis->RecordErrorLog(cError);

		return THREAD_RET_VALUE;
	}
	return THREAD_RET_VALUE;
}

/**
* @brief         事件分发循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcBusSwap::__EventAllotLoop()
{
	char cError[255]="";
	NX_EVENT_MESSAGE EventMsg;
	while ( !m_bEnd )
	{
		_ZERO_MEM(cError,255);
		if( m_EventDeque.empty() )
		{
			sy_sleep(50);
			continue;
		}

		EventMsg = m_EventDeque.front();
		
		if( NULL == m_pSubject )
		{
			break;
		}

		// 设备状态更新比对(避免各客户端都比对更新)
		if( ( EventMsg.n_msg_type == NX_IED_EVENT_COMMU_REPORT )  ||
			( EventMsg.n_msg_type == NX_SYS_EVENT_IED_RUNSTATUS_REPORT) 
		  )
		{
			if( !__DevStatusChgHandle(EventMsg) )   // 未变位
			{
				// 清除事件
				EventMsg.list_subfields.clear();
				m_EventDeque.pop_front();
				continue;
			}
		}

		// 对上通信不处理总线发来的与对上通信状态信息,避免BUS_SWAP观察者又将该类信息返回总线,多于1个通信服务在线时导致无限循环
		if( ( m_nRegisterType == -1 )  && (EventMsg.n_msg_type == NX_SYS_EVENT_ECU_COMMU_REPORT) &&
			( EventMsg.n_obj_type == NX_OBJ_TYPE_NX_ZHUZHAN )
		  )
		{
			sprintf(cError,"对从总线收到的对上通信状态报告(msg_type=%d event_obj=%d obj_type=%d)不予处理,丢弃",
				    EventMsg.n_msg_type,EventMsg.n_event_obj,EventMsg.n_obj_type);
			RecordTraceLog("对从总线收到的对上通信状态报告()不予处理,丢弃");
		}
		else
			m_pSubject->SendEventNotify(EventMsg);

		// 清除事件
		EventMsg.list_subfields.clear();
		m_EventDeque.pop_front();
	}

	RecordTraceLog("正常退出事件分发循环");
	return 0;
}

/**
* @brief         设备状态(运行和通信)变位处理(变化更新内存并上送,否则丢弃)
* @param[in]     NX_EVENT_MESSAGE &EventMsg:状态变位事件
* @return        bool true-变位并更新内存  false-未变位
*/
bool CNXEcBusSwap::__DevStatusChgHandle(IN NX_EVENT_MESSAGE &EventMsg)
{
	char cError[255]="";
	EC_STATUS_INFO tmpStatus;
	CTimeConvert  TmCvt;
	if( EventMsg.n_msg_type == NX_SYS_EVENT_IED_RUNSTATUS_REPORT )   // 运行状态
	{
		tmpStatus.eDevType = (DEV_CATEGORY)EventMsg.n_obj_type;
		tmpStatus.nDevID   = EventMsg.n_event_obj;
		tmpStatus.eStatusType = EC_RUN_STATUS;
		 
		if( m_pModelSeekIns->GetDevStatusInfo(tmpStatus) )   
		{
			if( EventMsg.n_backup == tmpStatus.nStatus )    // 比对未变位,不处理
				return false;
		}
		// 获取失败和变位都按变位处理(更新内存)
		tmpStatus.nStatus = EventMsg.n_backup;
		TmCvt.SetTime(EventMsg.n_send_utctm,0);
		TmCvt.GetStandStringTime(tmpStatus.strTime);
		if( !m_pModelSeekIns->UpdateDevStatusInfo(tmpStatus) )
		{
			sprintf(cError,"__DevStatusChgHandle():更新内存中devType=%d devId=%d设备的运行状态=%d time=%s时失败",
				    tmpStatus.eDevType,tmpStatus.nDevID,tmpStatus.nStatus,tmpStatus.strTime.c_str());
			RecordErrorLog(cError);
		}
		else
		{
			sprintf(cError,"__DevStatusChgHandle():设备devType=%d devId=%d的运行状态变位,值=%d time=%s,更新内存成功",
				    tmpStatus.eDevType,tmpStatus.nDevID,tmpStatus.nStatus,tmpStatus.strTime.c_str());
			RecordTraceLog(cError);
		}
		return true;
	}

	// 通信状态处理
	bool bChg = false;
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = EventMsg.list_subfields.begin();
	while ( ite != EventMsg.list_subfields.end() )
	{
		tmpStatus.eDevType = (DEV_CATEGORY)ite->n_obj_type;
		tmpStatus.nDevID   = ite->n_obj_id;
		tmpStatus.eStatusType = EC_COMMU_STATUS;
		if( m_pModelSeekIns->GetDevStatusInfo(tmpStatus) )   
		{
			if( ite->n_value == tmpStatus.nStatus )    // 比对未变位,不处理
			{
				++ ite;
				continue;
			}
		}
		// 获取失败和变位都按变位处理(更新内存)
		bChg = true;
		tmpStatus.nStatus = ite->n_value;
		tmpStatus.nReason = ite->n_field_type;
		TmCvt.SetTime(ite->n_curvalueutctm,0);
		TmCvt.GetStandStringTime(tmpStatus.strTime);
		if( !m_pModelSeekIns->UpdateDevStatusInfo(tmpStatus) )  // 更新
		{
			sprintf(cError,"__DevStatusChgHandle():更新内存中devType=%d devId=%d设备的通信状态=%d、原因=%d、时间=%s时失败",
				    tmpStatus.eDevType,tmpStatus.nDevID,tmpStatus.nStatus,tmpStatus.nReason,tmpStatus.strTime.c_str());
			RecordErrorLog(cError);
		}
		else
		{
			sprintf(cError,"__DevStatusChgHandle():设备devType=%d devId=%d通信状态变位,值=%d、原因=%d、时间=%s,更新内存成功",
				    tmpStatus.eDevType,tmpStatus.nDevID,tmpStatus.nStatus,tmpStatus.nReason,tmpStatus.strTime.c_str());
			RecordTraceLog(cError);
		}
		++ite;
	}
	return bChg;
}

/**
* @brief         命令分发处理线程回调执行函数
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     LPVOID pParam: 参数
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcBusSwap::__OnCmdHandleThreadExec(LPVOID pObj,LPVOID  pParam)
{
	char cError[255]    = "";

	if( NULL == pObj )
		return THREAD_RET_VALUE;

	CNXEcBusSwap * pThis = (CNXEcBusSwap *)pObj;

	try
	{
		pThis->__CmdHandleLoop();
	}
	catch(...)
	{
		sprintf(cError,"命令处理线程异常退出,原因:%s:(%d)",strerror(errno),errno);
		pThis->RecordErrorLog(cError);

		return THREAD_RET_VALUE;
	}
	return THREAD_RET_VALUE;
}

/**
* @brief         命令处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcBusSwap::__CmdHandleLoop()
{
	NX_COMMON_MESSAGE  CmdMsg,ResultMsg;
	int nRet = 0;
	char cError[255]="";
	while ( !m_bEnd )
	{
		if( m_CommonMsgDeque.empty() )
		{
			sy_sleep(100);
			continue;
		}
		CmdMsg = m_CommonMsgDeque.front();

		// 发送到总线
		try
		{
			nRet = m_MbClientLib.SendCommonMsg(CmdMsg);
		}
		catch(...)
		{
			RecordErrorLog("__CmdHandleLoop()中向总线发送命令时异常");
		}

		if( nRet != 0 )
		{
			sprintf(cError,"__CmdHandleLoop()发送命令到总线失败,自动生成失败回应.(命令:%s)",
				    _get_commonmsg_desc(CmdMsg).c_str());
			RecordErrorLog(cError);
			// 自动生成失败回应
			_init_common_msg_struct(ResultMsg);
			_make_commonmsg_failed_response(CmdMsg,ResultMsg);
			if( m_pSubject != NULL )
				m_pSubject->SendResult(ResultMsg);
			ResultMsg.list_subfields.clear();
		}
		else
		{
			sprintf(cError,"__CmdHandleLoop()发送命令到总线成功(命令:%s)",
				    _get_commonmsg_desc(CmdMsg).c_str());
			RecordTraceLog(cError);
		}
		// 清除命令
		CmdMsg.list_subfields.clear();
		m_CommonMsgDeque.pop_front();
		_ZERO_MEM(cError,255);
		nRet = 0;
		sy_sleep(5);
	}

	RecordTraceLog("正常退出命令处理循环");
	return 0;
}

/**
* @brief         加载对外通信相关共享库
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__InitEcLib()
{
	char cError[255]  = "";

	// 加载模型访问库
	m_pEcModelLib = LoadEcShareLib<CNXLoadEcModelLib,CLogRecord,char>(m_pLogRecord,SHARE_LIB_BUS_SWAP);
	if( m_pEcModelLib == NULL )
	{
		RecordErrorLog("__InitEcLib()时加载模型访问库失败");
		return COMMONERR_LOAD_LIB_FAILED;
	}
	
	// 创建模型查询实例
	m_pModelSeekIns = m_pEcModelLib->CreateStationModelSeekIns();
	if( NULL == m_pModelSeekIns )
	{
		RecordErrorLog("__InitEcLib()时实例化模型查询实例失败");
		return ECERR_READ_EC_MODEL_FAILED;
	}

	// 获取系统基本配置
	if( m_pModelSeekIns->GetBasicCfg(m_BasicCfg) )
	{
		// 根据配置重新初始化日志
		_SetLogRootPath(m_BasicCfg.str_logroot_path.c_str());
		_SetLogRecordDays(m_BasicCfg.n_rcd_days);
		_SetLogLevel(m_BasicCfg.n_log_level);
		_OpenLogRecord();
	}

	return 0;
}

/**
* @brief         释放对外通信相关共享库
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__FreeEcLib()
{
	// 释放查询实例
	if( (m_pEcModelLib != NULL ) && (m_pModelSeekIns != NULL) )
	{
		m_pEcModelLib->DestroyStationModelSeekIns(m_pModelSeekIns);
	}
	
	// 释放模型访问库
	FreeEcShareLib(m_pEcModelLib);

	return 0;
}

/**
* @brief         观察者对象资源分派，设置注册信息等
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__InitObserver()
{
	if( m_pObserver != NULL )
	{
		RecordErrorLog("__InitObserver()时观察者已经初始化，不再初始化");
		return 0;
	}

	// 分配资源
	m_pObserver = new CNXObserver(m_pLogRecord,BUS_SWAP_OBSERVER_ID);
	if( NULL == m_pObserver )
	{
		RecordErrorLog("__InitObserver()时为观察者分配资源失败");
		return COMMONERR_NEW_MEM_FAILED;
	}

	// 设置对象描述
	m_pObserver->SetRegObjDesc("BUS_SWAP观察者");

	// 设置关注事件信息
	EC_INFO_ORDER_LIST  EventInfoList;
	EventInfoList.push_back(NX_SYS_EVENT_ECU_COMMU_REPORT); // 与各客户端通信状态报告
	m_pObserver->SetCareEventType(true,EventInfoList);
	EventInfoList.clear();

	// 设置关注的设备(关注所有对外通信客户端)
	EC_DEV_ORDER_LIST DevList;
	DEV_UUID DevID;
	DevID.eDevType = DEV_R_CLIENT;
	DevID.nDevID   = ALL_DEV;
	DevList.push_back(DevID);
	m_pObserver->SetCareDev(true,DevList); // 关注所有远方客户端设备
	DevList.clear();

	// 设置处理回调
	m_pObserver->SetResultCallBack(this,__OnSubjectResultRecv);
	m_pObserver->SetEventCallBack(this,__OnSubjectEventRecv);

	// 注册到服务中介
	if( !m_pObserver->Init() )
	{
		return -1;
	}
	return 0;
}

/**
* @brief         目标者结果信息接收处理(由观察者回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     NX_COMMON_MESSAGE & ResultMsg:收到的结果信息
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__OnSubjectResultRecv(LPVOID pRegObj,NX_COMMON_MESSAGE & ResultMsg)
{
	string strLog;
	int  nRet        = 0;
	// 调用mb client接口发送结果到总线
	if( pRegObj == NULL )
	{
		printf("__OnSubjectResultRecv()中回调对象为NULL,无法接收目标者结果\n");
		return -1;
	}

	CNXEcBusSwap * pThis = (CNXEcBusSwap *)pRegObj;
	try
	{
		nRet = pThis->m_MbClientLib.SendCommonMsg(ResultMsg);
	}
	catch(...)
	{
		pThis->RecordErrorLog("__OnSubjectResultRecv()中向总线发送结果时异常");
		return -1;
	}

	if( nRet != 0 )
	{
		strLog = "__OnSubjectResultRecv()发送召唤结果到总线失败,内容如下:\n" + _get_commonmsg_desc(ResultMsg);
		pThis->RecordErrorLog(strLog.c_str());
	}
	else
	{
		strLog = "__OnSubjectResultRecv()发送召唤结果到总线成功,内容如下:\n" + _get_commonmsg_desc(ResultMsg);
		pThis->RecordTraceLog(strLog.c_str());
	}

	return nRet;
}

/**
* @brief         目标者事件信息接收处理(由观察者回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     NX_EVENT_MESSAGE & EventMsg:收到的事件信息
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__OnSubjectEventRecv(LPVOID pRegObj,NX_EVENT_MESSAGE & EventMsg)
{
	string strLog;
	int  nRet        = 0;
	// 调用mb client接口发送事件到总线
	if( pRegObj == NULL )
	{
		printf("__OnSubjectEventRecv()中回调对象为NULL,无法接收目标者事件\n");
		return -1;
	}

	CNXEcBusSwap * pThis = (CNXEcBusSwap *)pRegObj;
	try
	{
		nRet = pThis->m_MbClientLib.SendEventMsg(EventMsg);
	}
	catch(...)
	{
		pThis->RecordErrorLog("__OnSubjectEventRecv()中向总线发送事件时异常");
		return -1;
	}

	if( nRet != 0 )
	{
		strLog = "__OnSubjectEventRecv()发送变位事件到总线失败,内容如下:\n" + _get_eventmsg_desc(EventMsg);
		pThis->RecordErrorLog(strLog.c_str());
	}
	else
	{
		strLog = "__OnSubjectEventRecv()发送变位事件到总线成功,内容如下:\n" + _get_eventmsg_desc(EventMsg);
		pThis->RecordTraceLog(strLog.c_str());
	}

	return nRet;
}

/**
* @brief         释放观察者信息资源，从服务中介注销
* @param[in]     无
* @param[out]    无
* @return        int 0-成功 其它-失败
*/
int CNXEcBusSwap::__FreeObserver()
{
	if( m_pObserver != NULL )
	{
		m_pObserver->Exit();

		delete m_pObserver;

		m_pObserver = NULL;
	}
	return 0;
}
