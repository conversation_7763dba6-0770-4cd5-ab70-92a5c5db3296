/**********************************************************************
* NXEcNode.h         author:jjl      date:22/09/2013            
*---------------------------------------------------------------------
*  note: 对外通信远方客户端结点类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXECCLIENTNODE_H_ 
#define _H_NXECCLIENTNODE_H_

#include "ec_common_def.h"
#include "nx_model_def.h"
#include "CommuTransObj.h"
#include "NXLoadNetListenLib.h"
#include "NXEcNodeChannel.h"
#include "EcTemplateFunc.h"
#include "TimeConvert.h"
#include "ec_net_listen_def.h"

////////////////////////////////////////////////////////////////类型定义
/** @brief         通道配置映射表*/
typedef map<int,CNXEcNodeChannel*> EC_NODE_CHANNEL_MAP;

/** @brief         通道状态映射表*/
typedef map<int,EC_STATUS_INFO*> EC_CHANNEL_STATUS_MAP;

/** @brief               监听服务注册对象信息结构*/
typedef struct _CLINET_INFO
{
	/** @brief           连接的客户端名称*/
	string               strClientName;

	/** @brief           通道号*/
	int                  nChannelId;

	/** @brief           连接的通道IP*/
	string			     strClientIp;

	_CLINET_INFO()
	{
		strClientName       = "";
		strClientIp			= "";
		nChannelId          = -1;
	}

}CLINET_INFO;
typedef list <CLINET_INFO>CLINET_INFO_LIST;
/**
* @defgroup   CNXEcClientNode 远方客户端结点类
* @{
*/
 
/**
 * @brief      每个客户端结点内部各通道业务的启动、停止实现及多个通道通信状态的合成处理
 * <AUTHOR>
 * @date       22/09/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEcClientNode:public CNXECObject
{
	////////////////////////////////////////////////////////////////构造、析构
public:

	/**
	* @brief         构造函数
	* @param[in]     CLogRecord* pLogRecord:日志对象指针
	* @param[in]     ECU_CLIENT_TB& ClientCfg:客户端配置结构
	* @param[out]    无
	* @return        无
	*/
	CNXEcClientNode(IN CLogRecord* pLogRecord,IN ECU_CLIENT_TB& ClientCfg);
	/**
	* @brief         析构函数
	* @param[in]     无
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEcClientNode();
	////////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         初始化客户端相关配置(初始化目标者、注册到网络监听等)
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/

	bool Init();

	/**
	* @brief         启动各通道通信
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/

	bool Run();

	/**
	* @brief         停止各通道通信
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/

	bool Stop();

	/**
	* @brief         设置事件通知回调
	* @param[in]     LPVOID pRegObj：注册对象
	* @param[in]     PFUNC_ON_NXEVENT_MSG_HANDLE pCallBakFunc:事件信息通知回调
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	bool SetEventNotifyCallBak(IN LPVOID pRegObj,IN PFUNC_ON_NXEVENT_MSG_HANDLE pCallBakFunc);

	/**
	* @brief         获得指定命令的处理结果
	* @param[in]     NX_COMMON_MESSAGE & CmdMsg:命令信息
	* @param[out]    NX_COMMON_MESSAGE & ResultMsg:结果信息
	* @return        int 0-成功 其它-失败
	*/
	bool GetCommonMsgResult(IN NX_COMMON_MESSAGE & CmdMsg,OUT NX_COMMON_MESSAGE & ResultMsg);

	/**
	* @brief 传入模型查询指针.					
	* @note 
	**/
	void __SetModelSeekIns(INXEcModelMgr * pModelSeekIns);

	/////////////////////////////////////////////////////////////////保护方法
protected:

	/**
	* @brief         释放资源
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/
	bool          _ReleaseSource();
	/**
	* @brief         为各通道对象分派资源及初始化
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	bool           _InitChannel();

	/**
	* @brief         释放各通道对象资源
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	bool           _FreeChannel();

	/**
	* @brief         通道状态变化接收处理(由结点通道对象回调)
	* @param[in]     LPVOID pRegObj：注册对象
	* @param[in]     EC_STATUS_INFO& Status:收到的状态信息
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	static int     _OnChannelStatusChgRecv(IN LPVOID pRegObj,IN EC_STATUS_INFO& Status);  

	/**
	* @brief         加载网络监听共享库,并注册通知回调
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	int _InitNetListenLib();

	/**
	* @brief         注销回调并释放网络监听共享库
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	int _FreeNetListenLib();

	/**
	* @brief         收到远方网络连接接收处理(由网络监听库回调)
	* @param[in]     无
	* @param[out]    无
	* @return        void
	*/
	static void _OnRemoteConnectRecv(IN LPVOID pObj,IN ICommuTransObj* pConntObj,IN PUB_NETWORK_ADDR& ConntAddr); 

	/**
	* @brief         将要开辟的线程信息初始化后加入线程队列
	* @param[in]     无
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	int _InitThreadInfo();

	/**
	* @brief         周期上送各客户端通信状态处理线程回调执行函数
	* @param[in]     LPVOID pObj: 回调对象
	* @param[in]     LPVOID pParam: 参数
	* @return        int: 0-成功 其它：错误码
	*/
	static int  _OnStatusCycSendThreadExec(LPVOID pObj,LPVOID  pParam = NULL);

	/**
	* @brief         周期上送各客户端通信状态循环
	* @param[in]     无
	* @return        int: 0-成功 其它：错误码
	*/
	int _CycSendStatusLoop();

	/////////////////////////////////////////////////////////////////私有方法
private:
	/**
	* @brief         远方网络连接处理
	* @param[in]     无
	* @param[out]    无
	* @return        int 0:处理成功 其它:处理失败
	*/
	int __RemoteConnectHandle(IN ICommuTransObj* pConntObj,IN PUB_NETWORK_ADDR& ConntAddr);

	/**
	* @brief         发送状态报告
	* @param[in]     NX_EVENT_MESSAGE & EventMsg:状态报告事件
	* @return        int: 0-成功 其它：错误码
	*/
	int __SendStatusReport(IN NX_EVENT_MESSAGE & EventMsg);

	/**
	* @brief         生成状态报告事件
	* @param[out]    NX_EVENT_MESSAGE & EventMsg:状态报告事件
	* @param[in]     int nChannelID: -1：报告中包括全部通道(周期上送）,否则仅包含指定通道号(变位上送)
	* @return        int: 0-成功 其它：错误码
	*/
	int __MakeStatusReport(OUT NX_EVENT_MESSAGE & EventMsg,IN int nChannelID = -1);

	/**
	* @brief         根据状态信息结构更新指定通道状态
	* @param[in]     EC_STATUS_INFO & StatusInfo:状态信息
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	int __UpdateChannelStatus(IN EC_STATUS_INFO & StatusInfo);

	/**
	* @brief         响应状态召唤请求
	* @param[out]    NX_COMMON_MESSAGE & ResultMsg:结果回应
	* @return        int: 0-成功 其它：错误码
	*/
	int __AnswerStatusAsk(OUT NX_COMMON_MESSAGE & ResultMsg );

	/**
	* @brief         根据IP地址查找对应的通道号
	* @param[in]     const char* chIP:IP地址
	* @return        int: 通道对象ID,-1:失败
	*/
	int __GetChannelIDByIP(IN const char* chIP);

	/**
	* @brief         判断指定通道或本客户端的链路状态是否正常
	* @param[in]     bool bAllChannel:是否判断本客户端的链路的状态,默认为false,该值为true时,第一个参数无意义
	* @param[in]     int nChannelID:通道ID
	* @return        bool :true-连接状态,false-断开状态
	*/
	bool __LinkStatusIsConnected(IN int nChannelID,IN bool bClient = false);

	/**
	* @brief 从ecpro.ini文件中,读取安全测试相关的配置.					
	* @note 
	**/
	void __ReadIni();
	/**
	* @brief 将状态信息写入数据库.					
	* @note nFlag 0-标识重复连接入库 1-标识连接断开入库
	**/
	void __StatusToDb(IN int nStatus,IN string & strClient,IN string &strIp,IN int nChannelNo,IN int nFlag);


	//////////////////////////////////////////////////////////////////私有成员
private:

	/** @brief              结点配置信息*/
	ECU_CLIENT_TB *         m_pNodeCfg;

	/** @brief              通道对象映射表*/
	EC_NODE_CHANNEL_MAP     m_NodeChannelMap;

	/** @brief              通道状态映射表*/
	EC_CHANNEL_STATUS_MAP   m_ChannelStatusMap;

	/** @brief              通道状态互斥锁*/
	CThreadMutualLock       m_LockForStatus;

	/** @brief              网络监听服务对象指针*/
	CNXLoadNetListenLib *   m_pNetListenLib;

	/** @brief              网络监听库注册结构*/
	LISTEN_REG_OBJ_INFO     m_RegListenInfo;

	/** @brief              结点管理类回调对象*/
	LPVOID                  m_pNodeMgrObj;

	/** @brief              事件通知回调函数*/
	PFUNC_ON_NXEVENT_MSG_HANDLE m_pEventNotifyCallBak;

	/** @brief              结束标识*/
	bool                    m_bEnd;

	/** @brief              初始化标识*/
	bool                    m_bInit;

	/** @brief              记录审计日志标识*/
	bool                    m_bRecord;

	/** @brief              计算机名称*/
	string                  m_strComputer;

	/** @brief				模型查询实例对象指针*/
	INXEcModelMgr	 *		m_pMSeekIns;

	/** @brief				连接的客户端信息链表*/
	CLINET_INFO_LIST		lClientInfo;

};


/** @} */ //OVER



#endif   // _H_NXECCLIENTNODE_H_