/**********************************************************************
* NXEcNodeChannel.h         author:jjl      date:22/09/2013            
*---------------------------------------------------------------------
*  note: 对外通信结点下通道类头文件定义                                                              
*  
**********************************************************************/

#ifndef _H_NXECNODECHANNEL_H_ 
#define _H_NXECNODECHANNEL_H_

#include "ec_common_def.h"
#include "CommuTransObj.h"
#include "INXEcModelMgr.h"
#include "INXEcProtocol.h"
#include "NXEcLoadProLib.h"

///////////////////////////////////////////////////////////////////类型定义

/**
* @defgroup   CNXEcNodeChannel 客户端结点通道类
* @{
*/
 
/**
 * @brief      通信结点下各通道相关业务实现，包括启、停通信规约、判断通道状态等
 * <AUTHOR>
 * @date       22/09/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXEcNodeChannel:public CNXECObject
{
	////////////////////////////////////////////////////////////////构造、析构
public:

	/**
	* @brief         构造函数
	* @param[in]     CLogRecord* pLogRecord:日志对象指针
	* @param[in]     ECU_CLIENT_TB& ClientCfg:客户端配置结构指针
	* @param[in]     ECU_CHANNEL_TB * pChannelCfg:通道配置结构指针
	* @param[out]    无
	* @return        无
	*/
	CNXEcNodeChannel(IN CLogRecord* pLogRecord,IN ECU_CLIENT_TB* pCliCfg,IN ECU_CHANNEL_TB* pChannelCfg);
	/**
	* @brief         析构函数
	* @param[in]     无
	* @param[out]    无
	* @return        无
	*/
	virtual ~CNXEcNodeChannel();
	////////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         启动规约通信
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/

	virtual bool Run();

	/**
	* @brief         停止规约通信
	* @param[in]     无
	* @return        bool true-成功 false-失败
	*/

	virtual bool Stop();

	/**
	* @brief         初始化规约相关信息(如加载规约库、创建规约实例)
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	virtual bool Init();

	/**
	* @brief         设置通道状态变化回调(链路状态变化后回调上层)
	* @param[in]     LPVOID pRegObj：注册对象
	* @param[in]     pStatusChgCallBak :通道状态变化回调处理函数
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int SetStatusChgCallBak(IN LPVOID pRegObj,IN PFUNC_ON_CHANNEL_STATUS_CHG pStatusChgCallBak);

	/**
	* @brief         设置网络通信方式规约的通信对象
	* @param[in]     ICommuTransObj * pCommuObj:通信对象
	* @param[out]    无
	* @return        int 0-成功 其它-失败
	*/
	virtual int SetNetCommuObj(IN ICommuTransObj * pCommuObj);
	/////////////////////////////////////////////////////////////////保护方法
protected:

	
	 
	/////////////////////////////////////////////////////////////////私有方法
private:

	/**
	* @brief         加载通信规约共享库
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	bool __InitProLib();

	/**
	* @brief         释放通信规约共享库
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	bool __FreeProLib();

	/**
	* @brief         初始化规约启动参数
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	bool __InitProParam();

	/**
	* @brief         初始化通信配置参数
	* @param[in]     无
	* @param[out]    无
	* @return        bool true-成功 false-失败
	*/
	bool ___InitCommuCfgParam();
	
	//////////////////////////////////////////////////////////////////私有成员
private:

	/** @brief         通道配置*/
	ECU_CHANNEL_TB *   m_pChannelCfg;

	/** @brief         所属客户端配置*/
	ECU_CLIENT_TB  *   m_pClientCfg;

	/** @brief         所属客户端结点对象(回调用)*/
	LPVOID             m_pClientNodeObj;

	/** @brief         状态变化通知回调函数*/
	PFUNC_ON_CHANNEL_STATUS_CHG m_pStatusChgCallBck;

	/** @brief         规约加载对象*/
	CNXEcLoadProLib *  m_pLoadProLib;

	/** @brief         规约实例对象*/
	INXEcProtocol *    m_pProtocolIns;

	/** @brief         规约接口参数*/
	SRV_PRO_START_PARAM m_ProParam;

	/** @brief         初始化标识*/
	bool               m_bInit;

	/** @brief         是否启动标识*/
	bool               m_bStart;
};



/** @} */ //OVER



#endif    // _H_NXECNODECHANNEL_H_