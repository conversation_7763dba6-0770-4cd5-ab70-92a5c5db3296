/**********************************************************************
* NXEcSSModelSeek.cpp         author:jjl      date:11/09/2013            
*---------------------------------------------------------------------
*  note:变电站模型内部信息访问方法实现文件                                                                 
*  
**********************************************************************/

#include "NXEcSSModelSeek.h"

/**
* @brief         构造函数 
* @param[in]     CLogRecord * pLogRecord:日志对象指针
* @return        
*/
CNXEcSSModelSeek::CNXEcSSModelSeek(IN CLogRecord * pLogRecord)
	:CNXECObject(pLogRecord,"CNXEcSSModelSeek")
{
	m_pSubStation = NULL;
	m_pModelMgr   = NULL;

	_InitGTitleToTypeMap();
	SetRunTimeObjName(SHARE_LIB_MODEL_ACCESS);
}

/**
* @brief         析构函数
* @param[in]      
* @param[out]    
* @return        
*/
CNXEcSSModelSeek::~CNXEcSSModelSeek()
{
	m_GTitleMap.clear();
}


/**
* @brief         关联模型管理对象
* @param[in]     INXEcModelMgr * pModelMgr:模型管理对象指针
* @return        void
*/
void CNXEcSSModelSeek::AttachModelMgrObj( IN INXEcModelMgr * pModelMgr)
{
	if( NULL == pModelMgr )
		return ;

	m_pModelMgr = pModelMgr;

	// 默认关联首站
	m_pSubStation = m_pModelMgr->GetSubStationModel();
	
}

/**
* @brief         获取模型管理对象指针
* @param[in]     无
* @return        INXEcModelMgr * pModelMgr:模型管理对象指针
*/
INXEcModelMgr * CNXEcSSModelSeek::GetModelMgrObj()
{
	return m_pModelMgr;
}


/**
* @brief         关联变电站模型
* @param[in]     EC_SUBSTATION * pSubStation:要关联的变电站模型指针
* @return        void
*/
void CNXEcSSModelSeek::AttachSubStation(IN EC_SUBSTATION * pSubStation) 
{
	m_pSubStation = pSubStation;
}

/**
* @brief         关联指定变电站编号的变电站模型
* @param[in]     UINT nStationID:变电站编号
* @return        void
*/
void CNXEcSSModelSeek::AttachSubStation(IN  UINT nStationID) 
{
	if( NULL != m_pModelMgr )
		m_pSubStation = m_pModelMgr->GetSubStationModel(nStationID);

}

/**
* @brief         关联日志对象
* @param[in]     CLogRecord * pLogRecord:日志对象
* @return        void
*/
void CNXEcSSModelSeek::AttachLogRecord(IN CLogRecord * pLogRecord)
{
	SetLogRecord(pLogRecord);
}

/**
* @brief         获得当前变电站基本配置(不包含子模型)
* @param[in]     无
* @return        const SUBSTATION_TB* :变电站配置结构指针
*/
const SUBSTATION_TB* CNXEcSSModelSeek::GetSubStationBasicCfg()
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	return m_pSubStation->pStation;
}

/**
* @brief         获得厂站下的全部一次设备配置
* @param[out]    无
* @return        EC_PRIM_LIST *:一次设备链表
*/
const EC_PRIM_LIST * CNXEcSSModelSeek::GetPrimDevListOfStation()
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	return &m_pSubStation->PrimList;
}

/**
* @brief         获得指定编号的一次设备配置
* @param[in]     int nPrimID:指定的一次设备编号
* @param[out]    无
* @return        PRIMEQUIPMENT_TB *;一次设备配置
*/
const PRIMEQUIPMENT_TB* CNXEcSSModelSeek::GetPrimDevCfg( IN UINT nPrimID ) 
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	EC_PRIM_MAP::iterator ite = m_pSubStation->PrimMap.find(nPrimID);
	if( ite != m_pSubStation->PrimMap.end() )
		return ite->second;

	return NULL;
}

/**
* @brief         获得厂站下指定类型的一次设备链表
* @param[in]     TPRIM_TYPE ePrimType:指定的一次设备类型
* @param[out]    EC_PRIM_LIST & PrimList:指定类型的一次设备链表
* @return        int 0-成功 其它-失败
*/
int CNXEcSSModelSeek::GetPrimDevListByType(IN TPRIM_TYPE ePrimType,OUT EC_PRIM_LIST & PrimList)
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return -1;
	}

	const PRIMEQUIPMENT_TB * pTb = NULL;

	EC_PRIM_LIST::iterator ite = m_pSubStation->PrimList.begin();
	while (ite != m_pSubStation->PrimList.end())
	{
		pTb = *ite;

		if( NULL == pTb )
		{
			++ite;
			continue;
		}

		if( pTb->e_psrtype == ePrimType )
		{
			PrimList.push_back(pTb);
		}

		++ite;
	}

	return 0;
}

/**
* @brief         获取厂站下的全部二次设备链表
* @param[out]    无
* @return        EC_IED_LIST*:IED设备链表
*/
const EC_IED_LIST* CNXEcSSModelSeek::GetIedListOfStation()
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	return &m_pSubStation->IedList;
}

/**
* @brief         获取厂站下指定类型的IED设备基本配置链表(仅包含其LD配置)
* @param[out]    EC_IED_BASIC_LIST & IedList:IED基本信息指针链表.
* @param[in]     IedType: 由TSECONDDEV_TYPE枚举指定的类型.默认-1，表示取全部IED
* @return        int 0-成功 其它-失败
*/
int CNXEcSSModelSeek::GetIedBasicList( OUT EC_IED_BASIC_LIST & IedList,IN int nIedType/* = -1*/)
{
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return -1;
	}

	EC_IED_LIST::iterator ite = m_pSubStation->IedList.begin();

	while (ite != m_pSubStation->IedList.end())
	{
		if( -1  == nIedType )  // 全部IED
		{
			IedList.push_back((*ite)->pIed);
		}
		else                  // 按类型获取
		{
			if( (*ite)->pIed->e_psrtype == nIedType)
			{
				IedList.push_back((*ite)->pIed);
			}
		}
		++ite;
	}
	return 0;
}

/**
* @brief         根据设备编号获取IED的全部配置
* @param[in]     UINT nIedID:二次设备编号
* @param[out]    无
* @return        EC_IED* :二次设备配置结构指针
*/
const EC_IED * CNXEcSSModelSeek::GetIedCfgByID( IN UINT nIedID )
{
	char cError[255] = "";
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	char cMapId[100] = "";
	sprintf(cMapId,"%d",nIedID);
	
	EC_IED_MAP::iterator ite = m_pSubStation->IedMap.find(cMapId);
	if( ite != m_pSubStation->IedMap.end() )
		return ite->second;

	sprintf(cError,"GetIedCfgByID()指定的IED编号(%d)不存在",nIedID);
	RecordErrorLog(cError);

	return NULL;
}

/**
* @brief         根据103地址获取ied全部配置
* @param[in]     UINT nAddr103: 二次设备103地址
* @return        EC_IED* : 二次设备配置结构指针
*/
const EC_IED* CNXEcSSModelSeek::GetIedCfgByAddr103(IN UINT nAddr103)
{
	char cError[255] = "";
	if( NULL == m_pSubStation )
	{
		RecordErrorLog("变电站结构没有关联，无法获得对应配置");
		return NULL;
	}

	char cMapId[100] = "";
	sprintf(cMapId,"%d/%d",m_pSubStation->pStation->n_obj_id,nAddr103);

	EC_IED_MAP::iterator ite = m_pSubStation->IedMap.find(cMapId);
	if( ite != m_pSubStation->IedMap.end() )
		return ite->second;

	sprintf(cError,"GetIedCfgByAddr103()指定的IED 103地址(%d)不存在",nAddr103);
	RecordErrorLog(cError);

	return NULL;
}

/**
* @brief         根据设备编号获取IED的基本信息配置(包含ld基本信息)
* @param[in]     UINT nIedID:二次设备编号
* @param[out]    无
* @return        const IED_TB* :IED设备配置结构指针
*/
const IED_TB* CNXEcSSModelSeek::GetIedBasicCfgByID( IN UINT nIedID )
{
	// 查找IED
	const EC_IED * pEcIed = GetIedCfgByID(nIedID);
	if( pEcIed == NULL )
		return NULL;

	return pEcIed->pIed;
}

/**
* @brief         根据103地址获取ied基本配置信息(包含ld基本信息)
* @param[in]     UINT nAddr103: IED设备103地址
* @return        const IED_TB* : IED设备配置结构指针
*/
const IED_TB* CNXEcSSModelSeek::GetIedBasicCfgByAddr103(IN UINT nAddr103)
{
	// 查找IED
	const EC_IED * pEcIed = GetIedCfgByAddr103(nAddr103);
	if( pEcIed == NULL )
		return NULL;

	return pEcIed->pIed;
}

/**
* @brief         获取指定IED设备下指定LD的全部相关配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[out]    无
* @return        EC_LD* :IED设备LD配置结构指针
*/
const EC_LD * CNXEcSSModelSeek::GetIedLdAllCfg( IN UINT nIedID,IN UINT nLdID )
{
	char cError[255] = "";
	
	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定LD
	EC_LD_MAP::const_iterator iteLd = pIed->ldMap.find(nLdID);
	if( iteLd != pIed->ldMap.end() )
		return iteLd->second;
	
	sprintf(cError,"GetIedLdCfg()指定的IED LD编号(%d)不存在",nLdID);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定IED设备下指定LD的基本配置及全部信息点表配置列表(不含组标题)
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[out]    无
* @return        const LD_TB* :IED设备下LD配置结构指针
*/
const LD_TB * CNXEcSSModelSeek::GetIedLdModelCfg( IN UINT nIedID,IN UINT nLdID )
{
	// 查找IED下的LD配置
	const EC_LD * pEcLd = GetIedLdAllCfg(nIedID,nLdID);
	if( pEcLd == NULL )
		return NULL;

	return pEcLd->pLdCfg;
}

/**
* @brief         获取指定IED设备下指定LD的全部组标题配置列表
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[out]    无
* @return        const LIST_GTITILE * :IED设备下LD组标题配置链表指针
*/
const LIST_GTITILE * CNXEcSSModelSeek::GetIedLdGTitleCfg( IN UINT nIedID,IN UINT nLdID )
{
	// 查找IED下的LD配置
	const EC_LD * pEcLd = GetIedLdAllCfg(nIedID,nLdID);
	if( pEcLd == NULL )
		return NULL;

	return &pEcLd->vGTitleList;
}

/**
* @brief         获取指定IED设备下指定LD中指定编号的模拟量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nAiId: ai编号
* @return        ANALOG_TB* :二次设备模拟量配置结构指针
*/
const ANALOG_TB* CNXEcSSModelSeek::GetIedAiCfg( IN UINT nIedID, IN UINT nLdID, IN UINT nAiId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定AI
	sprintf(cMapId,"%d/%d",nLdID,nAiId);
	EC_AI_MAP::const_iterator ite = pIed->aiMap.find(cMapId);
	if( ite != pIed->aiMap.end() )
		return ite->second;

	sprintf(cError,"GetIedAiCfg() IED=%d LD=%d aiID=%d的信息不存在",nIedID,nLdID,nAiId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定组号/条目号的模拟量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nGroup: 组号
* @param[in]     UINT nItem:  条目号
* @return        ANALOG_TB* :二次设备模拟量配置结构指针
*/
const ANALOG_TB* CNXEcSSModelSeek::GetIedAiCfg( IN UINT nIedID, IN UINT nLdID,IN UINT nGroup,IN UINT nItem)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定AI
	sprintf(cMapId,"%d/G%d/%d",nLdID,nGroup,nItem);
	EC_AI_MAP::const_iterator ite = pIed->aiMap.find(cMapId);
	if( ite != pIed->aiMap.end() )
		return ite->second;

	sprintf(cError,"GetIedAiCfg() IED=%d LD=%d group=%d item=%d的信息不存在",nIedID,nLdID,nGroup,nItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的定值配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nSgId: 定值编号
* @return        SG_TB* :二次设备定值配置结构指针
*/
const SG_TB* CNXEcSSModelSeek::GetIedSgCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nSgId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定SG
	sprintf(cMapId,"%d/%d",nLdID,nSgId);
	EC_SG_MAP::const_iterator ite = pIed->sgMap.find(cMapId);
	if( ite != pIed->sgMap.end() )
		return ite->second;

	sprintf(cError,"GetIedSgCfg() IED=%d LD=%d sgID=%d的信息不存在",nIedID,nLdID,nSgId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定组号/条目号的定值配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nGroup: 组号
* @param[in]     UINT nItem:  条目号
* @return        SG_TB* :二次设备定值配置结构指针
*/
const SG_TB* CNXEcSSModelSeek::GetIedSgCfg(IN UINT nIedID,IN UINT nLdID,IN UINT nGroup,IN UINT nItem)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定SG
	sprintf(cMapId,"%d/G%d/%d",nLdID,nGroup,nItem);
	EC_SG_MAP::const_iterator ite = pIed->sgMap.find(cMapId);
	if( ite != pIed->sgMap.end() )
		return ite->second;

	sprintf(cError,"GetIedSgCfg() IED=%d LD=%d group=%d item=%d的信息不存在",nIedID,nLdID,nGroup,nItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的开关量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nDiId: 开关量编号
* @return        STRAP_TB* :二次设备开关量配置结构指针
*/
const STRAP_TB* CNXEcSSModelSeek::GetIedDiCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nDiId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定DI
	sprintf(cMapId,"%d/%d",nLdID,nDiId);
	EC_DI_MAP::const_iterator ite = pIed->diMap.find(cMapId);
	if( ite != pIed->diMap.end() )
		return ite->second;

	sprintf(cError,"GetIedDiCfg() IED=%d LD=%d diID=%d的信息不存在",nIedID,nLdID,nDiId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定fun/inf或group/item的开关量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nFunGroup: 功能类型或组号(功能类型与信息序号组合,组号与条目号组合，不可交叉组合)
* @param[in]     UINT nInfItem:  信息序号或条目号
* @param[in]     int  nParamType:参数类型 1：标识FUN/INF组合 2:GROUP/ITEM组合 默认为1
* @return        STRAP_TB* :二次设备开关量配置结构指针
*/
const STRAP_TB* CNXEcSSModelSeek::GetIedDiCfg(IN UINT nIedID, IN UINT nLdID,IN UINT nFunGroup,IN UINT nInfItem,IN int nParamType)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	if( nParamType == 1 ) // fun/inf组合
	{
		// 查找指定DI (LD编号/F功能类型/信息序号)
		sprintf(cMapId,"%d/F%d/%d",nLdID,nFunGroup,nInfItem);
		EC_DI_MAP::const_iterator ite = pIed->diMap.find(cMapId);
		if( ite != pIed->diMap.end() )
			return ite->second;
	}
	else if( nParamType == 2 )  // group/item组合
	{
		// 按(LD编号/G组号/条目号)查找
		sprintf(cMapId,"%d/G%d/%d",nLdID,nFunGroup,nInfItem);
		EC_DI_MAP::const_iterator ite = pIed->diMap.find(cMapId);
		if( ite != pIed->diMap.end() )
			return ite->second;
	}

	sprintf(cError,"GetIedDiCfg() IED=%d LD=%d FunGroup=%d InfItem=%d的信息不存在",nIedID,nLdID,nFunGroup,nInfItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的软压板配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nSfId: 软压板编号
* @return        STRAP_TB* :二次设备软压板配置结构指针
*/
const STRAP_TB* CNXEcSSModelSeek::GetIedSoftStrapCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nSfId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定soft
	sprintf(cMapId,"%d/%d",nLdID,nSfId);
	EC_SOFTSTRAP_MAP::const_iterator ite = pIed->softMap.find(cMapId);
	if( ite != pIed->softMap.end() )
		return ite->second;

	sprintf(cError,"GetIedSoftStrapCfg() IED=%d LD=%d softID=%d的信息不存在",nIedID,nLdID,nSfId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定fun/inf或group/item的软压板配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nFunGroup: 功能类型或组号(功能类型与信息序号组合,组号与条目号组合，不可交叉组合)
* @param[in]     UINT nInfItem:  信息序号或条目号
* @param[in]     int  nParamType:参数类型 1：标识FUN/INF组合 2:GROUP/ITEM组合 默认为2
* @return        STRAP_TB* :二次设备软压板配置结构指针
*/
const STRAP_TB* CNXEcSSModelSeek::GetIedSoftStrapCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nFunGroup,IN UINT nInfItem,IN int nParamType)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	if( nParamType == 1 ) // fun/inf组合
	{
		// 按 (LD编号/F功能类型/信息序号)
		sprintf(cMapId,"%d/F%d/%d",nLdID,nFunGroup,nInfItem);
		EC_SOFTSTRAP_MAP::const_iterator ite = pIed->softMap.find(cMapId);
		if( ite != pIed->softMap.end() )
			return ite->second;
	}
	else if( nParamType == 2 )  // group/item组合
	{
		// 按(LD编号/G组号/条目号)查找
		sprintf(cMapId,"%d/G%d/%d",nLdID,nFunGroup,nInfItem);
		EC_SOFTSTRAP_MAP::const_iterator ite = pIed->softMap.find(cMapId);
		if( ite != pIed->softMap.end() )
			return ite->second;
	}

	sprintf(cError,"GetIedSoftStrapCfg() IED=%d LD=%d FunGroup=%d InfItem=%d的信息不存在",nIedID,nLdID,nFunGroup,nInfItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的事件量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nEventId: 事件量编号
* @return        EVENT_TB* :二次设备事件量配置结构指针
*/
const EVENT_TB* CNXEcSSModelSeek::GetIedEventCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nEventId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定event
	sprintf(cMapId,"%d/%d",nLdID,nEventId);
	EC_EVENT_MAP::const_iterator ite = pIed->eventMap.find(cMapId);
	if( ite != pIed->eventMap.end() )
		return ite->second;

	sprintf(cError,"GetIedEventCfg() IED=%d LD=%d eventID=%d的信息不存在",nIedID,nLdID,nEventId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定fun/inf或group/item的事件量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nFunGroup: 功能类型或组号(功能类型与信息序号组合,组号与条目号组合，不可交叉组合)
* @param[in]     UINT nInfItem:  信息序号或条目号
* @param[in]     int  nParamType:参数类型 1：标识FUN/INF组合 2:GROUP/ITEM组合 默认为1
* @return        EVENT_TB* :二次设备事件量配置结构指针
*/
const EVENT_TB* CNXEcSSModelSeek::GetIedEventCfg(IN UINT nIedID, IN UINT nLdID,IN UINT nFunGroup,IN UINT nInfItem,IN int nParamType)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	if( nParamType == 1 ) // fun/inf组合
	{
		// 按(LD编号/F功能类型/信息序号)
		sprintf(cMapId,"%d/F%d/%d",nLdID,nFunGroup,nInfItem);
		EC_EVENT_MAP::const_iterator ite = pIed->eventMap.find(cMapId);
		if( ite != pIed->eventMap.end() )
			return ite->second;
	}
	else if( nParamType == 2 )  // group/item组合
	{
		// 按(LD编号/G组号/条目号)查找
		sprintf(cMapId,"%d/G%d/%d",nLdID,nFunGroup,nInfItem);
		EC_EVENT_MAP::const_iterator ite = pIed->eventMap.find(cMapId);
		if( ite != pIed->eventMap.end() )
			return ite->second;
	}

	sprintf(cError,"GetIedEventCfg() IED=%d LD=%d FunGroup=%d InfItem=%d的信息不存在",nIedID,nLdID,nFunGroup,nInfItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的告警量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nAlarmId: 告警量编号
* @return        ALARM_TB* :二次设备告警量配置结构指针
*/
const ALARM_TB* CNXEcSSModelSeek::GetIedAlarmCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nAlarmId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定alarm
	sprintf(cMapId,"%d/%d",nLdID,nAlarmId);
	EC_ALARM_MAP::const_iterator ite = pIed->alramMap.find(cMapId);
	if( ite != pIed->alramMap.end() )
		return ite->second;

	sprintf(cError,"GetIedAlarmCfg() IED=%d LD=%d alarmID=%d的信息不存在",nIedID,nLdID,nAlarmId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定fun/inf或group/item的告警量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nFunGroup: 功能类型或组号(功能类型与信息序号组合,组号与条目号组合，不可交叉组合)
* @param[in]     UINT nInfItem:  信息序号或条目号
* @param[in]     int  nParamType:参数类型 1：标识FUN/INF组合 2:GROUP/ITEM组合 默认为1
* @return        ALARM_TB* :二次设备告警量配置结构指针
*/
const ALARM_TB* CNXEcSSModelSeek::GetIedAlarmCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nFunGroup,IN UINT nInfItem,IN int nParamType)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	if( nParamType == 1 ) // fun/inf组合
	{
		// 按(LD编号/F功能类型/信息序号)
		sprintf(cMapId,"%d/F%d/%d",nLdID,nFunGroup,nInfItem);
		EC_ALARM_MAP::const_iterator ite = pIed->alramMap.find(cMapId);
		if( ite != pIed->alramMap.end() )
			return ite->second;
	}
	else if( nParamType == 2 )  // group/item组合
	{
		// 按(LD编号/G组号/条目号)查找
		sprintf(cMapId,"%d/G%d/%d",nLdID,nFunGroup,nInfItem);
		EC_ALARM_MAP::const_iterator ite = pIed->alramMap.find(cMapId);
		if( ite != pIed->alramMap.end() )
			return ite->second;
	}

	sprintf(cError,"GetIedAlarmCfg() IED=%d LD=%d FunGroup=%d InfItem=%d的信息不存在",nIedID,nLdID,nFunGroup,nInfItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的定值区号配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nZoneId: 定值区编号
* @return        SGZONE_TB* :二次设备定值区号配置结构指针
*/
const SGZONE_TB * CNXEcSSModelSeek::GetIedZoneCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nZoneId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定zone
	sprintf(cMapId,"%d/%d",nLdID,nZoneId);
	EC_ZONE_MAP::const_iterator ite = pIed->zoneMap.find(cMapId);
	if( ite != pIed->zoneMap.end() )
		return ite->second;

	sprintf(cError,"GetIedZoneCfg() IED=%d LD=%d zoneID=%d的信息不存在",nIedID,nLdID,nZoneId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定组号/条目号的定值区号配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nGroup: 组号
* @param[in]     UINT nItem:  条目号
* @return        SGZONE_TB* :二次设备定值区号配置结构指针
*/
const SGZONE_TB * CNXEcSSModelSeek::GetIedZoneCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nGroup,IN UINT nItem)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定zone
	sprintf(cMapId,"%d/G%d/%d",nLdID,nGroup,nItem);
	EC_ZONE_MAP::const_iterator ite = pIed->zoneMap.find(cMapId);
	if( ite != pIed->zoneMap.end() )
		return ite->second;

	sprintf(cError,"GetIedZoneCfg() IED=%d LD=%d group=%d item=%d的信息不存在",nIedID,nLdID,nGroup,nItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定编号的故障量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nFaultId: 故障量编号
* @return        FAULTTAG_TB* :二次设备故障量配置结构指针
*/
const FAULTTAG_TB * CNXEcSSModelSeek::GetIedFaultCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nFaultId)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定fault
	sprintf(cMapId,"%d/%d",nLdID,nFaultId);
	EC_FAULT_MAP::const_iterator ite = pIed->faultMap.find(cMapId);
	if( ite != pIed->faultMap.end() )
		return ite->second;

	sprintf(cError,"GetIedFaultCfg() IED=%d LD=%d faultID=%d的信息不存在",nIedID,nLdID,nFaultId);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         获取指定二次设备下指定cpu中指定组号/条目号的故障量配置
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nGroup: 组号
* @param[in]     UINT nItem:  条目号
* @return        FAULTTAG_TB* :二次设备故障量配置结构指针
*/
const FAULTTAG_TB * CNXEcSSModelSeek::GetIedFaultCfg(IN UINT nIedId, IN UINT nLdID, IN UINT nGroup,IN UINT nItem)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedId);
	if( pIed == NULL )
		return NULL;

	// 查找指定fault
	sprintf(cMapId,"%d/G%d/%d",nLdID,nGroup,nItem);
	EC_FAULT_MAP::const_iterator ite = pIed->faultMap.find(cMapId);
	if( ite != pIed->faultMap.end() )
		return ite->second;

	sprintf(cError,"GetIedFaultCfg() IED=%d LD=%d group=%d item=%d的信息不存在",nIedId,nLdID,nGroup,nItem);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         根据二次设备编号、cpu及组号获取组配置信息
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     UINT nLdID: CPU编号
* @param[in]     UINT nGroup: 组号
* @return        ECU_GTITLE_TB* :二次设备组配置结构指针
*/
const ECU_GTITLE_TB * CNXEcSSModelSeek::GetIedGTitleCfg(IN UINT nIedID, IN UINT nLdID, IN UINT nGroup)
{
	char cError[255] = "";
	char cMapId[100] ="";

	// 查找IED
	const EC_IED * pIed = GetIedCfgByID(nIedID);
	if( pIed == NULL )
		return NULL;

	// 查找指定group title
	sprintf(cMapId,"%d/%d",nLdID,nGroup);
	EC_GROUP_MAP::const_iterator ite = pIed->groupMap.find(cMapId);
	if( ite != pIed->groupMap.end() )
		return ite->second;

	sprintf(cError,"GetIedGTitleCfg() IED=%d LD=%d group=%d的信息不存在",nIedID,nLdID,nGroup);
	RecordErrorLog(cError);
	return NULL;
}

/**
* @brief         根据IED编号、LD编号、组号获得命令消息类型
* @param[in]     UINT nIedID:二次设备编号
* @param[in]     int  nLdId:LD编号
* @param[in]     int  nGroup:组号
* @return        int： 组标题类型
*/
int CNXEcSSModelSeek::GetMsgTypeByGroup(IN UINT nIedID, IN UINT nLdID, IN UINT nGroup)
{
	bool bFind = false;
	const ECU_GTITLE_TB * pTitle = GetIedGTitleCfg(nIedID,nLdID,nGroup);

	if( pTitle == NULL )
	{
		return EC_GROUP_INVALID;
	}

	GTITLE2TYPE_MAP::iterator ite = m_GTitleMap.begin();
	while (ite != m_GTitleMap.end() )
	{
		if( NULL == strstr( pTitle->str_gt_name.c_str(),ite->first) )
		{
			++ite;
			continue;
		}
		bFind = true;
		break;
	}

	// 匹配到关键字，则直接返回对应的键值
	if( bFind )
	{
		return ite->second;
	}

	// 没有匹配到任何关键字时，再判断是否为“定值”或"状态量"
	if( NULL != strstr(pTitle->str_gt_name.c_str(), "定值") )
	{
		return EC_GROUP_SG;
	}
	else if( NULL != strstr(pTitle->str_gt_name.c_str(), "状态量") )
	{
		return EC_GROUP_STATUS;
	}

	return EC_GROUP_INVALID;
}

/**
* @brief         初始化组标题关键字名称与组类型映射关系
* @param[in]     无
* @return        void
*/
void CNXEcSSModelSeek::_InitGTitleToTypeMap()
{
	m_GTitleMap.clear();

	// "定值"和"状态量"关键字暂不加入，因其它关键字可能包含这两个关键字,当map中匹配不到时再判断是否为这两个
	//m_GTitleMap["定值"]       = EC_GROUP_SG;
	m_GTitleMap["定值区号"]   = EC_GROUP_ZONE;
	m_GTitleMap["软压板"]     = EC_GROUP_SOFT;
	m_GTitleMap["模拟量"]     = EC_GROUP_AI;
	m_GTitleMap["告警"]       = EC_GROUP_ALARM;
	m_GTitleMap["自检"]       = EC_GROUP_ALARM;
	m_GTitleMap["动作"]       = EC_GROUP_EVENT;
	m_GTitleMap["跳闸"]       = EC_GROUP_EVENT;
	m_GTitleMap["开关量"]     = EC_GROUP_DI;
	m_GTitleMap["开入量"]     = EC_GROUP_DI;
	m_GTitleMap["硬压板"]     = EC_GROUP_HARD;
	//m_GTitleMap["状态量"]     = EC_GROUP_STATUS;
	m_GTitleMap["故障量"]     = EC_GROUP_FAULT;
	m_GTitleMap["装置配置"]   = EC_GROUP_IED;
	m_GTitleMap["线路配置"]   = EC_GROUP_LINE;
	m_GTitleMap["母线配置"]   = EC_GROUP_BUS;
	m_GTitleMap["开关配置"]   = EC_GROUP_BREAK;
	m_GTitleMap["刀闸配置"]   = EC_GROUP_SWITCH;
	m_GTitleMap["变压器"]     = EC_GROUP_TRANS;
	m_GTitleMap["发电机"]     = EC_GROUP_GENERATOR;
	m_GTitleMap["电容器"]     = EC_GROUP_COMP;
	m_GTitleMap["电抗器"]     = EC_GROUP_REACTOR;
	m_GTitleMap["子站配置"]   = EC_GROUP_SUBSTATION;
}



/**
* @brief         获得系统基本配置
* @param[out]    BASIC_CFG_TB& basicCfg:保存系统基本配置信息;
* @return        bool true-成功 false-失败
*/
bool CNXEcSSModelSeek::GetBasicCfg(OUT BASIC_CFG_TB& basicCfg)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}

	return m_pModelMgr->GetBasicCfg(basicCfg);
}

/**
* @brief         获得指定功能的超时时间(单位：秒)
* @param[in]     UINT nFun:指定的功能编号(功能号参见inxmb.def.h定义)
* @return        int:超时时间(秒)
*/
int CNXEcSSModelSeek::GetFunTimeOut( IN UINT nFun)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
	return m_pModelMgr->GetFunTimeOut(nFun);
}

/**
* @brief         获得指定通信状态变化编号的原因描述
* @param[in]     UINT nReasonID:指定的原因编号(原因号参见数据库表定义)
* @param[out]    OUT string &strReason:原因描述
* @return        bool true-成功 false-失败
*/
bool CNXEcSSModelSeek::GetComuStatusReason( IN UINT nResonID,OUT string &strReason)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}

	return m_pModelMgr->GetComuStatusReason(nResonID,strReason);
}

/**
* @brief         获取对外通信所有客户端配置链表指针
* @brief[out]    LIST_CLIENT & : 对外通信客户端配置链表
* @return        bool true-成功 false-失败
*/
bool CNXEcSSModelSeek::GetWholeEcClientList(OUT LIST_CLIENT & ClientList)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
    return m_pModelMgr->GetWholeEcClientList(ClientList);
}

/**
* @brief         获取对外通信监听端口配置
* @brief[out]    LIST_LISTEN & : 对外通信监听端口配置链表
* @return        bool true-成功 false-失败
*/
bool CNXEcSSModelSeek::GetEcListenList( OUT LIST_LISTEN & ltnList) 
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
	return m_pModelMgr->GetEcListenList(ltnList);
}

/**
* @brief         获取变电站基本信息链表(不包括任何子模型)
* @param[out]    LIST_SUBSTATION &StationList:保存变电站列表基本信息
* @return        bool :true-成功 false-失败
*/
bool CNXEcSSModelSeek::GetSubStationList(OUT LIST_SUBSTATION &StationList)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
	return m_pModelMgr->GetSubStationList(StationList);
}

/**
* @brief         获取指定设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构中指定设备及状态类型
* @param[out]    EC_STATUS_INFO & statusInfo:状态信息结构中输出对应状态的值
* @return        true :true-成功 false-失败
*/
bool  CNXEcSSModelSeek::GetDevStatusInfo( IN OUT EC_STATUS_INFO & statusInfo )
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
	return m_pModelMgr->GetDevStatusInfo(statusInfo);
}

/**
* @brief         设置指定设备的状态信息
* @param[in]     EC_STATUS_INFO & statusInfo:状态信息结构
* @param[out]    无
* @return        true :true-成功 false-失败
*/
bool  CNXEcSSModelSeek::UpdateDevStatusInfo( IN EC_STATUS_INFO & statusInfo )
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}
	return m_pModelMgr->UpdateDevStatusInfo(statusInfo);
}

/**
* @brief         获得变电站所有IED状态列表
* @param[out]    IED_LIST & IedList：IED列表
* @return        true :true-成功 false-失败
*/
bool  CNXEcSSModelSeek::GetAllIedStatus( OUT LIST_IED & IedList)
{
	if( NULL == m_pModelMgr )
	{
		RecordErrorLog("模型管理对象实例没有初始化,无法获取配置");
		return false;
	}

	if( NULL == m_pSubStation )
	{
		RecordErrorLog("没有关联变电站，无法获得对应配置");
		return false;
	}

	return m_pModelMgr->GetAllIedStatus(IedList,m_pSubStation->pStation->n_obj_id);
}