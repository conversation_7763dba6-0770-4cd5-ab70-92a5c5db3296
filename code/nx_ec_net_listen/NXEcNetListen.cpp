/**********************************************************************
* NXEcNetListen.cpp         author:jjl      date:30/09/2013            
*---------------------------------------------------------------------
*  note:网络端口监听类实现文件定义                                                               
*  
**********************************************************************/

#include "NXEcNetListen.h"

/**
* @brief         构造函数(禁止外部实例)
* @param[in]     CLogRecord * pLogRecord:日志对象指针
* @param[in]     int nReserve: 备用
* @return        无
*/
CNXEcNetListen::CNXEcNetListen(IN CLogRecord * pLogRecord ,IN int nReserve)
	:CNXECObject(NULL,"CNXEcNetListen")
{
	m_pModelSeekIns  = NULL;
	m_pEcModelLib    = NULL;
	m_pLoadCommuLib  = NULL;
	m_bInit          = false;
	m_bStart         = false;
	m_bSelfLog       = false;

	// 日志对象可以由主进程第一次创建该实例时传入,日志即可与主进程写在同一文件中
	// 如果不使用主进程传入的日志对象初始化CNXECObject,则自己创建单独日志
	if( NULL == m_pLogRecord ) // 创建独立日志
	{
		if( NULL != pLogRecord ) 
		{
			_SetLogPrint2Screen(pLogRecord->GetLogPrint2Screen());// 获取是否打印到屏幕标识
			_SetLogRecordDays(pLogRecord->GetLogRecordDays());
			_SetLogLevel(pLogRecord->GetLogRecordLevel());
			_SetLogFileMaxMb(pLogRecord->GetLogRecordFileSize()/1024);
			string strRoot;
			pLogRecord->GetLogRecordFileRootPath(strRoot);
			_SetLogRootPath(strRoot.c_str());
		}
		// 模块名
		_SetLogModuleName("net_listen");
		_OpenLogRecord();
		m_bSelfLog = true;
	}

	SetRunTimeObjName(SHARE_LIB_NET_LISTEN);
}

/**
* @brief         析构函数
* @param[in]     无
* @param[out]    
* @return        无
*/
CNXEcNetListen::~CNXEcNetListen()
{
}

/**
* @brief         获取网络监听类单实例指针
* @param[in]     CLogRecord * pLogRecord:日志对象指针
* @param[in]     int nReserve: 备用
* @return        CNXEcNetListen * :对象指针
*/
CNXEcNetListen* CNXEcNetListen::GetNXEcNetListenIns(IN CLogRecord * pLogRecord ,IN int nReserve)
{
	if ( NULL == CNXEcNetListen::sm_pListenIns )
	{
		CNXEcNetListen::sm_pListenIns = new CNXEcNetListen(pLogRecord,nReserve);
		if( CNXEcNetListen::sm_pListenIns != NULL)
		{
			if( !CNXEcNetListen::sm_pListenIns->_Init() )
			{
				delete CNXEcNetListen::sm_pListenIns;
				CNXEcNetListen::sm_pListenIns = NULL;
			}
		}
	}
	return CNXEcNetListen::sm_pListenIns;
}

/**
* @brief         启动网络监听业务
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::StartNetListen()
{
	char cError[255] = "";
	OPEN_PORT_RESULT eRet = R_UNKNOWN_FAIL;
	ICommuTransObj * pCommuObj = NULL;

	if( !_Init() )
	{
		RecordErrorLog("StartNetListen():模块未成功初始化,无法启动");
		return false;
	}
	if( m_bStart )
		return true;

	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.begin();
	while( ite != m_PortListenMap.end() )
	{
		pCommuObj = ite->second;
		if( pCommuObj == NULL )
		{
			++ite;
			continue;
		}

		eRet = pCommuObj->OpenPort();
		if( R_SUCCESS != eRet )
		{
			sprintf(cError,"StartNetListen():启动监听端口:%d失败，原因:%s(%d)",
				    ite->first,_get_commu_err_info(eRet).c_str(),eRet);
			RecordErrorLog(cError);
			return false;
		}

		sprintf(cError,"StartNetListen():启动监听端口:%d成功",ite->first);
		RecordTraceLog(cError);

		_ZERO_MEM(cError,255);
		pCommuObj = NULL;
		eRet = R_UNKNOWN_FAIL;
		++ite;
	}

	m_bStart = true;
	return true;
}

/**
* @brief         停止网络监听业务
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::StopNetListen()
{
	char cError[255] = "";
	int nRet = 0;
	bool bOk = true;
	ICommuTransObj * pCommuObj = NULL;

	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.begin();
	while( ite != m_PortListenMap.end() )
	{
		pCommuObj = ite->second;
		if( pCommuObj == NULL )
		{
			++ite;
			continue;
		}

		nRet = pCommuObj->ClosePort();
		if( 0 != nRet )
		{
			sprintf(cError,"StopNetListen():停止监听端口:%d失败",ite->first);
			RecordErrorLog(cError);
			bOk = false;
		}
		else
		{
			sprintf(cError,"StopNetListen():停止监听端口:%d成功",ite->first);
			RecordTraceLog(cError);
		}

		_ZERO_MEM(cError,255);
		pCommuObj = NULL;
		++ite;
	}

	// 释放资源
	_ReleaseSource();
	m_bStart = false;
	return bOk;
}

/**
* @brief         注册外部连接回调信息
* @param[in]     LISTEN_REG_OBJ_INFO & RegInfo:注册对象信息结构
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::RegConntCallBack(IN LISTEN_REG_OBJ_INFO & RegInfo)
{
	char cError[255] = "";
	ICommuTransObj * pCommuObj = NULL;
	int  nRet = -1;
	char * pIp = NULL;

	EC_LISTEN_IP2PORT_MAP::iterator ite = RegInfo.IpToPortMap.begin();
	while ( ite != RegInfo.IpToPortMap.end() )
	{
		// 判断注册的IP地址是否为关心所有外部连接IP地址
		if( ite->first.compare(STR_ALL_CONNECT_IP) == 0 )
			pIp = NULL;
		else
			pIp = (char*)ite->first.c_str();

		// 根据注册方提供的端口号查找对应的监听对象
		pCommuObj = __GetCommuObjByPortNo( ite->second );
		if( pCommuObj == NULL )
		{
			// 找不到时，将该信息注册给所有监听对象
			sprintf(cError,"RegConntCallBack():注册对象:%s 要求向PORT=%d的端口注册IP=%s的连接通知,但监听表中没有该端口的配置信息,将该信息注册到所有在监听的端口中",
				    RegInfo.strObjDes.c_str(),ite->second,ite->first.c_str());
			RecordErrorLog(cError);
			if( !_RegToAllListenObj(RegInfo.strObjDes,RegInfo.pRegObj,RegInfo.pNotifyCallBackFunc,pIp) )
			{
				return false;
			}
			++ite;
			_ZERO_MEM(cError,255);
			pIp = NULL;
			continue;
		}
		
		// 注册给指定的监听对象
		nRet = pCommuObj->RegisterListenCallBack(RegInfo.pRegObj,RegInfo.pNotifyCallBackFunc,pIp);
		if( nRet != 0 )
		{
			sprintf(cError,"RegConntCallBack():向监听端口对象(port=%d)注册对象:%s(ip=%s)的回调信息失败",
				    ite->second,RegInfo.strObjDes.c_str(),ite->first.c_str());
			RecordErrorLog(cError);
			return false;
		}
		else
		{
			sprintf(cError,"RegConntCallBack():向监听端口对象(port=%d)注册对象:%s(ip=%s)的回调信息成功",
				    ite->second,RegInfo.strObjDes.c_str(),ite->first.c_str());
			RecordTraceLog(cError);
		}
		++ite;
		pCommuObj = NULL;
		_ZERO_MEM(cError,255);
		nRet = -1;
		pIp = NULL;
	}

	return true;
}

/**
* @brief         注销指定端口对象下指定IP连接的回调信息
* @param[in]     const string & strIP:指定的IP地址
* @param[in]     int nLinstenPort:指定的监听端口
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::CancelConntCallBack(IN const string & strIP,IN int nListenPort)
{
	char cError[255] = "";
	ICommuTransObj * pCommuObj = NULL;
	int  nRet = -1;
	char * pIp = NULL;
	bool bOk = false;

	// 判断注册的IP地址是否为关心所有外部连接IP地址
	if( strIP.compare(STR_ALL_CONNECT_IP) == 0 )
		pIp = NULL;
	else
		pIp = (char*)strIP.c_str();

	// 根据注册方提供的端口号查找对应的监听对象
	pCommuObj = __GetCommuObjByPortNo( nListenPort );
	if( pCommuObj == NULL )
	{
		// 找不到时,所有监听对象中都注销
		sprintf(cError,"CancelConntCallBack():要求从PORT=%d的端口注销IP=%s的连接通知,但监听表中没有该端口的配置信息,将该信息从所有在监听的端口中注销",
			    nListenPort,strIP.c_str());
		RecordErrorLog(cError);

		return _CancelFromAllListeObj( pIp );
	}
	// 从指定对象中注销
	nRet = pCommuObj->CancelListenCallBack(pIp);
	if(nRet != 0 )
	{
		sprintf(cError,"CancelConntCallBack():从监听端口(port=%d)对象中注销IP=%s的回调失败",
			    nListenPort,strIP.c_str());
		RecordErrorLog(cError);
	}
	else
	{
		sprintf(cError,"CancelConntCallBack():从监听端口(port=%d)对象中注销IP=%s的回调成功",
			    nListenPort,strIP.c_str());
		RecordTraceLog(cError);
		bOk = true;
	}
	return bOk;
}

/**
* @brief         完成读取监听端口配置、初始化监听对象等准备工作
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::_Init()
{
	if( m_bInit )        // 已经初始化过
	{
		return true;
	}
	// 加载模型访问库
	if( !__InitEcModelLib() )
		return false;

	// 读取监听端口配置
	if( !__ReadListenPortCfg() )
		return false;

	// 加载通信库
	if( !__InitCommuLib() )
		return false;

	// 创建通信对象
	if( !__CreateListenObj() )
		return false;

	m_bInit = true;

	RecordTraceLog("初始化网络监听库成功");
	return true;
}

/**
* @brief         释放开辟的资源
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::_ReleaseSource()
{
	// 销毁监听对象
	__FreeListenObj();

	// 释放通信库
	__FreeCommuLib();

	// 释放端口配置
	__FreeListenPortCfg();

	// 卸载模型访问库
	__FreeEcModelLib();

	if( m_bSelfLog )
		_CloseLogRecord();

	m_bInit = false;
	return true;
}

/**
* @brief         加载模型查询库并创建查询实例
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__InitEcModelLib()
{
	m_pEcModelLib = LoadEcShareLib<CNXLoadEcModelLib,CLogRecord,char>(m_pLogRecord,SHARE_LIB_NET_LISTEN);
	if( NULL == m_pEcModelLib )
	{
		RecordErrorLog("__InitEcModelLib()加载模型访问库失败");
		return false;
	}

	// 创建模型查询实例
	m_pModelSeekIns = m_pEcModelLib->CreateStationModelSeekIns();
	if( NULL == m_pModelSeekIns )
	{
		RecordErrorLog("__InitEcModelLib()创建模型查询实例失败");
		return false;
	}

	RecordTraceLog("__InitEcLib()加载模型访问库及实例化模型查询实例成功");
	return true;
}

/**
* @brief         销毁查询实例并释放模型查询库
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__FreeEcModelLib()
{
	// 释放查询实例
	if( (m_pEcModelLib != NULL ) && (m_pModelSeekIns != NULL) )
	{
		m_pEcModelLib->DestroyStationModelSeekIns(m_pModelSeekIns);
	}

	// 释放模型访问库
	FreeEcShareLib(m_pEcModelLib);
	return true;
}

/**
* @brief         读取监听端口配置
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__ReadListenPortCfg()
{
	if( !m_pModelSeekIns->GetEcListenList(m_PortCfgList) )
	{
		RecordErrorLog("__ReadListenPortCfg()：获取端口监听配置信息失败");
		return false;
	}

	return true;
}

/**
* @brief         释放监听端口配置
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__FreeListenPortCfg()
{
	m_PortCfgList.clear();
	return true;
}

/**
* @brief         加载通信封装库
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__InitCommuLib()
{
	m_pLoadCommuLib = new CSYCommunDllPack(m_pLogRecord);
	if( NULL == m_pLoadCommuLib )
	{
		RecordErrorLog("__InitCommuLib():为通信封装类分派空间失败");
		return false;
	}

	if( !m_pLoadCommuLib->Load() )
	{
		RecordErrorLog("__InitCommuLib():加载平台通信库失败");
		return false;
	}

	RecordTraceLog("加载平台通信库成功");
	return true;
}

/**
* @brief         释放通信封装库
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__FreeCommuLib()
{
	if( NULL != m_pLoadCommuLib )
	{
		m_pLoadCommuLib->Release();
		delete m_pLoadCommuLib;
		m_pLoadCommuLib = NULL;
	}

	return true;
}

/**
* @brief         创建监听对象
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__CreateListenObj()
{
	char cError[255] = "";
	ECU_LISTEN_TB * pListenTb = NULL;
	ICommuTransObj * pCommuObj = NULL;
	int nIpLen = 0;
	COMMU_TCP_SERVER_PARAM SrvParam;

	LIST_LISTEN::iterator ite = m_PortCfgList.begin();
	while( ite != m_PortCfgList.end() )
	{
		pListenTb = (ECU_LISTEN_TB *)&(*ite);
		if( pListenTb->n_ls_port <= 0 )
		{
			++ite;
			continue;
		}

		// 确认该监听端口对象是否已经存在
		if( __GetCommuObjByPortNo(pListenTb->n_ls_port) != NULL )
		{
			sprintf(cError,"__CreateListenObj():IP=%s PORT=%d的服务端监听对象已经存在，不重复创建",
				    pListenTb->str_ls_ip.c_str(),pListenTb->n_ls_port);
			RecordErrorLog(cError);
			++ite;
			continue;
		}

		// 设置服务端对象参数
		SrvParam.LocalListenAddr.nPortNo = pListenTb->n_ls_port;
		nIpLen = ( pListenTb->str_ls_ip.size() > 32) ? 32 : pListenTb->str_ls_ip.size();
		memcpy(SrvParam.LocalListenAddr.cIP,pListenTb->str_ls_ip.c_str(),nIpLen );
		//设置收发超时无限(监听socket创建后默认设置接收超时为5秒,linux下accept时会每5秒解除一次阻塞,设置大超时避免频繁返回)
		SrvParam.TimeOut.RevTimeout = 0x7fffffff;
		SrvParam.TimeOut.SendTimeout = 0x7fffffff;
	
		// 创建通信对象
		pCommuObj = m_pLoadCommuLib->CreateTcpServerTranObj(&SrvParam,m_pLogRecord,OBJ_MODLE_P2P);
		if( NULL == pCommuObj )
		{
			sprintf(cError,"__CreateListenObj():创建IP=%s PORT=%d的服务端监听对象失败",
				    pListenTb->str_ls_ip.c_str(),pListenTb->n_ls_port);
			RecordErrorLog(cError);
			return false;
		}

		// 设置对象参数
		pCommuObj->SetOption(ADDR_REUSE,1);     // 支持地址复用

		// 加入映射表
		m_PortListenMap[pListenTb->n_ls_port] = pCommuObj;
		sprintf(cError,"__CreateListenObj():创建IP=%s PORT=%d的服务端监听对象成功,并加入映射表",
			    pListenTb->str_ls_ip.c_str(),pListenTb->n_ls_port);
		RecordTraceLog(cError);

		pCommuObj = NULL;
		pListenTb = NULL;
		_ZERO_MEM(cError,255);
		nIpLen = 0;
		_ZERO_MEM(SrvParam.LocalListenAddr.cIP,sizeof(SrvParam.LocalListenAddr.cIP));
		SrvParam.LocalListenAddr.nPortNo = 0;
		++ite;
	}

	RecordTraceLog("创建监听对象成功");
	return true;
}

/**
* @brief         释放监听对象
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::__FreeListenObj()
{
	char cError[255] = "";
	int nRet = 0;
	bool bOk = true;
	ICommuTransObj * pCommuObj = NULL;

	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.begin();
	while( ite != m_PortListenMap.end() )
	{
		pCommuObj = ite->second;
		if( pCommuObj == NULL )
		{
			++ite;
			continue;
		}

		nRet = m_pLoadCommuLib->DeleteCommuTranObj(pCommuObj);
		if( 0 != nRet )
		{
			sprintf(cError,"__FreeListenObj():销毁监听端口:%d对象失败",ite->first);
			RecordErrorLog(cError);
			bOk = false;
		}
		else
		{
			sprintf(cError,"__FreeListenObj():销毁监听端口:%d对象成功",ite->first);
			RecordTraceLog(cError);
		}

		_ZERO_MEM(cError,255);
		pCommuObj = NULL;
		++ite;
	}

	m_PortListenMap.clear();

	return bOk;
}

/**
* @brief         根据端口号查找对应的通信对象
* @param[in]     int nPortNo:端口号
* @param[out]    无
* @return        ICommuTransObj * :通信对象
*/
ICommuTransObj *CNXEcNetListen::__GetCommuObjByPortNo(IN int nPortNo)
{
	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.find(nPortNo);
	if( ite != m_PortListenMap.end() )
	{
		return ite->second;
	}

	return NULL;
}

/**
* @brief						向所有监听对象注册连接回调
* @param[in] strObjDes          注册对象描述
* @param[in] pRegObj			注册者对象指针
* @param[in] pListenCallBack	注册监听回调函数
* @param[in] pCareIP		    该回调函数关注的监听IP信息.为NULL:监听到任何IP连接都回调\n
非空时，仅pCareIP指定的IP连接时回调.注册过的IP(包括为NULL),重复\n
注册会失败,可先注销后再注册\n
* @return						true为成功；false：失败。
*/
bool CNXEcNetListen::_RegToAllListenObj(IN string& strOjbDes,IN void* pRegObj, IN _ONLISTENCLIENT pListenCallBack,IN char* pCareIP)
{
	char cError[255] = "";
	ICommuTransObj * pCommuObj = NULL;
	int nRet = -1;
	bool bOk = true;

	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.begin();
	while( ite != m_PortListenMap.end() )
	{
		pCommuObj = ite->second;
		if( pCommuObj == NULL )
		{
			++ite;
			continue;
		}

		nRet = pCommuObj->RegisterListenCallBack(pRegObj,pListenCallBack,pCareIP);
		if( 0 != nRet )
		{
			sprintf(cError,"_RegToAllListenObj():向监听端口对象(port=%d)注册对象:%s(IP=%s)的回调失败",
				    ite->first,strOjbDes.c_str(),pCareIP);
			RecordErrorLog(cError);
			bOk = false;
		}
		else
		{
			sprintf(cError,"_RegToAllListenObj():向监听端口对象(port=%d)注册对象:%s(IP=%s)的回调成功",
				    ite->first,strOjbDes.c_str(),pCareIP);
			RecordTraceLog(cError);
		}

		_ZERO_MEM(cError,255);
		pCommuObj = NULL;
		nRet = -1;
		++ite;
	}

	return bOk;
}

/**
* @brief         所有监听对象都注销指定的IP回调通知
* @param[in]     char * pIp:指定的IP地址
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcNetListen::_CancelFromAllListeObj( char * pIp )
{
	char cError[255] = "";
	ICommuTransObj * pCommuObj = NULL;
	int nRet = -1;
	bool bOk = true;

	EC_LISTEN_PORT2OBJ_MAP::iterator ite = m_PortListenMap.begin();
	while( ite != m_PortListenMap.end() )
	{
		pCommuObj = ite->second;
		if( pCommuObj == NULL )
		{
			++ite;
			continue;
		}

		nRet = pCommuObj->CancelListenCallBack(pIp);
		if( 0 != nRet )
		{
			sprintf(cError,"_CancelFromAllListeObj():从监听端口对象(port=%d)注销IP=%s的回调失败",
				    ite->first,pIp);
			RecordErrorLog(cError);
			bOk = false;
		}
		else
		{
			sprintf(cError,"_CancelFromAllListeObj():从监听端口对象(port=%d)注销IP=%s的回调成功",
				    ite->first,pIp);
			RecordTraceLog(cError);
		}

		_ZERO_MEM(cError,255);
		pCommuObj = NULL;
		nRet = -1;
		++ite;
	}

	return bOk;
}