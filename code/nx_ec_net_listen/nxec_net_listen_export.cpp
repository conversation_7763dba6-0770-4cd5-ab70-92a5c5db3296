/**********************************************************************
* nxec_net_listen_export.cpp         author:jjl      date:30/09/2013            
*---------------------------------------------------------------------
*  note: 网络监听共享库对外接口实现文件                                                               
*  
**********************************************************************/

#include "ec_net_listen_def.h"
#include "NXEcNetListen.h"

////////////////////////////////////////////////////////////////静态成员定义
CNXEcNetListen * CNXEcNetListen::sm_pListenIns = NULL;

CNXEcNetListen::CEcNetListenGarbage CNXEcNetListen::sm_ListenGarbage;

/**
* @brief         初始化网络监听库
* @param[in]     CLogRecord * pLogRecord:日志对象指针
* @param[in]     int nReserve: 备用
* @param[out]    无
* @return        bool: true-成功 false-失败
*/
bool InitNetListen(IN CLogRecord * pLogRecord,IN int nReserve)
{
	CNXEcNetListen * pIns = CNXEcNetListen::GetNXEcNetListenIns(pLogRecord,nReserve);
	if( pIns == NULL )
		return false;

	return true;
}

/**
* @brief         启动网络监听业务
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool StartNetListen()
{
	bool bOk = false;
	CNXEcNetListen * pIns = CNXEcNetListen::GetNXEcNetListenIns();
	if( pIns != NULL )
	{
		bOk = pIns->StartNetListen();
	}

	return bOk;
}

/**
* @brief         停止网络监听业务
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool StopNetListen()
{
	bool bOk = false;
	CNXEcNetListen * pIns = CNXEcNetListen::GetNXEcNetListenIns();
	if( pIns != NULL )
	{
		bOk = pIns->StopNetListen();
	}

	return bOk;
}

/**
* @brief         注册外部连接回调信息
* @param[in]     LISTEN_REG_OBJ_INFO & RegInfo:注册对象信息结构
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool RegConntCallBack(IN LISTEN_REG_OBJ_INFO & RegInfo)
{
	bool bOk = false;
	CNXEcNetListen * pIns = CNXEcNetListen::GetNXEcNetListenIns();
	if( pIns != NULL )
	{
		bOk = pIns->RegConntCallBack(RegInfo);
	}

	return bOk;
}

/**
* @brief         注销指定端口下的指定IP连接的回调信息
* @param[in]     const string & strIP:指定的IP地址
* @param[in]     int nLinstenPort:指定的监听端口
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CancelConntCallBack(IN const string &strIP,IN int nListenPort)
{
	bool bOk = false;
	CNXEcNetListen * pIns = CNXEcNetListen::GetNXEcNetListenIns();
	if( pIns != NULL )
	{
		bOk = pIns->CancelConntCallBack(strIP,nListenPort);
	}

	return bOk;
}
